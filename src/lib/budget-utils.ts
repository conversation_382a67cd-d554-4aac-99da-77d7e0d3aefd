import { BudgetItem, BudgetTemplate, BudgetReportItem } from '@/lib/types';
import { exportData, convertToCSV, convertToExcel, convertToPDF, downloadFile } from '@/lib/export-utils';

/**
 * Budget utility functions for data processing and export
 */

export interface BudgetExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  includeAccountDetails?: boolean;
  includePeriodInfo?: boolean;
  customColumns?: string[];
  filename?: string;
}

/**
 * Export budget items with various formatting options
 */
export async function exportBudgetItems(
  items: BudgetItem[],
  options: BudgetExportOptions
): Promise<void> {
  const {
    format,
    includeAccountDetails = true,
    includePeriodInfo = true,
    customColumns,
    filename = `budget-items-${new Date().toISOString().split('T')[0]}`
  } = options;

  // Define available columns
  const availableColumns = {
    'account.name': 'Account Name',
    'account.code': 'Account Code',
    'year': 'Year',
    'month': 'Month',
    'period': 'Period',
    'amount': 'Amount',
    'notes': 'Notes',
    'created_at': 'Created Date',
    'updated_at': 'Updated Date'
  };

  // Determine which columns to include
  let columnsToInclude: string[];
  if (customColumns) {
    columnsToInclude = customColumns;
  } else {
    columnsToInclude = ['account.name'];
    if (includeAccountDetails) {
      columnsToInclude.push('account.code');
    }
    if (includePeriodInfo) {
      columnsToInclude.push('year', 'month', 'period');
    }
    columnsToInclude.push('amount', 'notes', 'created_at');
  }

  // Transform data for export
  const exportData = items.map(item => {
    const transformedItem: Record<string, any> = {};
    
    columnsToInclude.forEach(column => {
      switch (column) {
        case 'account.name':
          transformedItem['Account Name'] = item.account?.name || item.account?.account_name || 'Unknown Account';
          break;
        case 'account.code':
          transformedItem['Account Code'] = item.account?.code || item.account?.account_code || '';
          break;
        case 'year':
          transformedItem['Year'] = item.year;
          break;
        case 'month':
          transformedItem['Month'] = item.month;
          break;
        case 'period':
          transformedItem['Period'] = new Date(item.year, item.month - 1).toLocaleDateString('default', {
            month: 'long',
            year: 'numeric'
          });
          break;
        case 'amount':
          transformedItem['Amount'] = item.amount;
          break;
        case 'notes':
          transformedItem['Notes'] = item.notes || '';
          break;
        case 'created_at':
          transformedItem['Created Date'] = new Date(item.created_at || item.createdAt || '').toLocaleDateString();
          break;
        case 'updated_at':
          transformedItem['Updated Date'] = new Date(item.updated_at || item.updatedAt || '').toLocaleDateString();
          break;
        default:
          if (availableColumns[column as keyof typeof availableColumns]) {
            transformedItem[availableColumns[column as keyof typeof availableColumns]] = (item as any)[column] || '';
          }
      }
    });

    return transformedItem;
  });

  // Export using the appropriate format
  switch (format) {
    case 'csv':
      const csv = convertToCSV(exportData);
      downloadFile(csv, `${filename}.csv`, 'text/csv');
      break;
    case 'excel':
      const excel = await convertToExcel(exportData);
      downloadFile(excel, `${filename}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      break;
    case 'pdf':
      const pdf = await convertToPDF(exportData, undefined, 'Budget Items Report');
      downloadFile(pdf, `${filename}.pdf`, 'application/pdf');
      break;
  }
}

/**
 * Export budget template with items
 */
export async function exportBudgetTemplate(
  template: BudgetTemplate,
  format: 'csv' | 'excel' | 'pdf' = 'csv'
): Promise<void> {
  const filename = `budget-template-${template.name.toLowerCase().replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}`;
  
  // Transform template items for export
  const exportData = template.items.map(item => ({
    'Account ID': item.accountId,
    'Amount': item.amount,
    'Notes': item.notes || ''
  }));

  // Add template metadata as header rows for CSV/Excel
  const metadata = [
    { 'Account ID': 'Template Name:', 'Amount': template.name, 'Notes': '' },
    { 'Account ID': 'Description:', 'Amount': template.description || '', 'Notes': '' },
    { 'Account ID': 'Created:', 'Amount': new Date(template.createdAt).toLocaleDateString(), 'Notes': '' },
    { 'Account ID': 'Items Count:', 'Amount': template.items.length.toString(), 'Notes': '' },
    { 'Account ID': '', 'Amount': '', 'Notes': '' }, // Empty row
    ...exportData
  ];

  switch (format) {
    case 'csv':
      const csv = convertToCSV(metadata);
      downloadFile(csv, `${filename}.csv`, 'text/csv');
      break;
    case 'excel':
      const excel = await convertToExcel(metadata);
      downloadFile(excel, `${filename}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      break;
    case 'pdf':
      const pdf = await convertToPDF(exportData, undefined, `Budget Template: ${template.name}`);
      downloadFile(pdf, `${filename}.pdf`, 'application/pdf');
      break;
  }
}

/**
 * Calculate budget variance and analysis
 */
export function calculateBudgetVariance(
  budgetedAmount: number,
  actualAmount: number
): {
  variance: number;
  variancePercent: number;
  status: 'over' | 'under' | 'on-target';
} {
  const variance = actualAmount - budgetedAmount;
  const variancePercent = budgetedAmount !== 0 ? (variance / budgetedAmount) * 100 : 0;
  
  let status: 'over' | 'under' | 'on-target' = 'on-target';
  if (Math.abs(variancePercent) > 5) { // 5% threshold
    status = variance > 0 ? 'over' : 'under';
  }

  return {
    variance,
    variancePercent,
    status
  };
}

/**
 * Generate budget summary statistics
 */
export function generateBudgetSummary(items: BudgetItem[]): {
  totalBudgeted: number;
  itemCount: number;
  averageAmount: number;
  accountsCount: number;
  periodRange: { startYear: number; endYear: number; startMonth: number; endMonth: number } | null;
} {
  if (!items.length) {
    return {
      totalBudgeted: 0,
      itemCount: 0,
      averageAmount: 0,
      accountsCount: 0,
      periodRange: null
    };
  }

  const totalBudgeted = items.reduce((sum, item) => sum + item.amount, 0);
  const uniqueAccounts = new Set(items.map(item => item.account_id || item.accountId));
  
  // Find period range
  const years = items.map(item => item.year);
  const months = items.map(item => item.month);
  
  return {
    totalBudgeted,
    itemCount: items.length,
    averageAmount: totalBudgeted / items.length,
    accountsCount: uniqueAccounts.size,
    periodRange: {
      startYear: Math.min(...years),
      endYear: Math.max(...years),
      startMonth: Math.min(...months),
      endMonth: Math.max(...months)
    }
  };
}

/**
 * Validate budget item data
 */
export function validateBudgetItem(item: Partial<BudgetItem>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!item.account_id && !item.accountId) {
    errors.push('Account is required');
  }

  if (!item.year || item.year < 1900 || item.year > 2100) {
    errors.push('Valid year is required (1900-2100)');
  }

  if (!item.month || item.month < 1 || item.month > 12) {
    errors.push('Valid month is required (1-12)');
  }

  if (item.amount === undefined || item.amount === null) {
    errors.push('Amount is required');
  } else if (typeof item.amount !== 'number' || item.amount < 0) {
    errors.push('Amount must be a positive number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Format currency for display
 */
export function formatBudgetAmount(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}

/**
 * Generate period comparison data
 */
export function compareBudgetPeriods(
  currentItems: BudgetItem[],
  previousItems: BudgetItem[]
): {
  currentTotal: number;
  previousTotal: number;
  variance: number;
  variancePercent: number;
  accountComparisons: Array<{
    accountId: string;
    accountName: string;
    current: number;
    previous: number;
    variance: number;
    variancePercent: number;
  }>;
} {
  const currentTotal = currentItems.reduce((sum, item) => sum + item.amount, 0);
  const previousTotal = previousItems.reduce((sum, item) => sum + item.amount, 0);
  const variance = currentTotal - previousTotal;
  const variancePercent = previousTotal !== 0 ? (variance / previousTotal) * 100 : 0;

  // Group by account for comparison
  const currentByAccount = new Map<string, { amount: number; name: string }>();
  const previousByAccount = new Map<string, { amount: number; name: string }>();

  currentItems.forEach(item => {
    const accountId = item.account_id || item.accountId || '';
    const accountName = item.account?.name || item.account?.account_name || 'Unknown Account';
    currentByAccount.set(accountId, {
      amount: (currentByAccount.get(accountId)?.amount || 0) + item.amount,
      name: accountName
    });
  });

  previousItems.forEach(item => {
    const accountId = item.account_id || item.accountId || '';
    const accountName = item.account?.name || item.account?.account_name || 'Unknown Account';
    previousByAccount.set(accountId, {
      amount: (previousByAccount.get(accountId)?.amount || 0) + item.amount,
      name: accountName
    });
  });

  // Create account comparisons
  const allAccountIds = new Set([...currentByAccount.keys(), ...previousByAccount.keys()]);
  const accountComparisons = Array.from(allAccountIds).map(accountId => {
    const current = currentByAccount.get(accountId)?.amount || 0;
    const previous = previousByAccount.get(accountId)?.amount || 0;
    const accountVariance = current - previous;
    const accountVariancePercent = previous !== 0 ? (accountVariance / previous) * 100 : 0;
    const accountName = currentByAccount.get(accountId)?.name || previousByAccount.get(accountId)?.name || 'Unknown Account';

    return {
      accountId,
      accountName,
      current,
      previous,
      variance: accountVariance,
      variancePercent: accountVariancePercent
    };
  });

  return {
    currentTotal,
    previousTotal,
    variance,
    variancePercent,
    accountComparisons
  };
}
