import { createApi } from '@reduxjs/toolkit/query/react';
import { Asset, AssetStatus } from '@/lib/types';
import { authenticatedBaseQuery } from '../baseQuery';

// Define the response type for paginated assets
export interface AssetsResponse {
  assets: Asset[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Define query parameters for assets list
export interface AssetsQueryParams {
  search?: string;
  status?: AssetStatus;
  depreciation_method?: string;
  purchase_date_from?: string;
  purchase_date_to?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Define the response type for the depreciation endpoint
interface DepreciationResponse {
  success: boolean;
  message: string;
  journal_entries_created_count: number;
}

// Define the response type for the dispose endpoint
interface DisposeAssetResponse {
  success: boolean;
  message: string;
  journal_entry_id: string;
}

export const assetsApi = createApi({
  reducerPath: 'assetsApi',
  baseQuery: authenticatedBaseQuery,
  tagTypes: ['Asset'],
  endpoints: (builder) => ({
    // Get assets with pagination and filtering
    getAssets: builder.query<AssetsResponse, AssetsQueryParams | void>({
      query: (params = {}) => {
        let url = 'assets';
        const queryParams = new URLSearchParams();

        // Add search parameter
        if (params.search) {
          queryParams.append('search', params.search);
        }

        // Add status filter
        if (params.status) {
          queryParams.append('status', params.status);
        }

        // Add depreciation method filter
        if (params.depreciation_method) {
          queryParams.append('depreciation_method', params.depreciation_method);
        }

        // Add date range filters
        if (params.purchase_date_from) {
          queryParams.append('purchase_date_from', params.purchase_date_from);
        }
        if (params.purchase_date_to) {
          queryParams.append('purchase_date_to', params.purchase_date_to);
        }

        // Add pagination parameters
        if (params.page) {
          queryParams.append('page', params.page.toString());
        }
        if (params.limit) {
          queryParams.append('limit', params.limit.toString());
        }

        // Add sorting parameters
        if (params.sortBy) {
          queryParams.append('sortBy', params.sortBy);
        }
        if (params.sortOrder) {
          queryParams.append('sortOrder', params.sortOrder);
        }

        // Append query string if there are parameters
        if (queryParams.toString()) {
          url += `?${queryParams.toString()}`;
        }

        return url;
      },
      // Transform the Go backend response to match frontend expectations
      transformResponse: (response: any): AssetsResponse => {
        return {
          assets: response.Data || [],
          pagination: {
            total: response.Total || 0,
            page: response.Page || 1,
            limit: response.Limit || 10,
            totalPages: response.TotalPages || 0,
            hasNextPage: response.Page < response.TotalPages,
            hasPreviousPage: response.Page > 1,
          },
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.assets.map(({ id }) => ({ type: 'Asset' as const, id })),
              { type: 'Asset', id: 'LIST' },
            ]
          : [{ type: 'Asset', id: 'LIST' }],
    }),

    // Get a specific asset by ID
    getAssetById: builder.query<Asset, string>({
      query: (id) => `assets/${id}`,
      providesTags: (result, error, id) => [{ type: 'Asset', id }],
    }),

    // Create a new asset
    createAsset: builder.mutation<Asset, Partial<Asset>>({
      query: (body) => ({
        url: 'assets',
        method: 'POST',
        body,
      }),
      invalidatesTags: [{ type: 'Asset', id: 'LIST' }],
    }),

    // Update an asset
    updateAsset: builder.mutation<Asset, { id: string; body: Partial<Asset> }>({
      query: ({ id, body }) => ({
        url: `assets/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Asset', id },
        { type: 'Asset', id: 'LIST' },
      ],
    }),

    // Delete an asset
    deleteAsset: builder.mutation<void, string>({
      query: (id) => ({
        url: `assets/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Asset', id },
        { type: 'Asset', id: 'LIST' },
      ],
    }),

    // Run depreciation
    runDepreciation: builder.mutation<DepreciationResponse, { run_until_date: string; asset_ids?: string[] }>({
      query: (body) => ({
        url: 'assets/depreciation',
        method: 'POST',
        body,
      }),
      invalidatesTags: [{ type: 'Asset', id: 'LIST' }],
    }),

    // Dispose/sell an asset
    disposeAsset: builder.mutation<DisposeAssetResponse, { id: string; disposal_date: string; disposal_proceeds: number }>({
      query: ({ id, ...body }) => ({
        url: `assets/${id}/dispose`,
        method: 'POST',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Asset', id },
        { type: 'Asset', id: 'LIST' },
      ],
    }),

    // Batch update assets
    batchUpdateAssets: builder.mutation<{ updated_count: number }, { asset_ids: string[]; status?: AssetStatus; [key: string]: any }>({
      query: (body) => ({
        url: 'assets/batch-update',
        method: 'POST',
        body,
      }),
      invalidatesTags: [{ type: 'Asset', id: 'LIST' }],
    }),

    // Batch delete assets
    batchDeleteAssets: builder.mutation<{ deleted_count: number }, { ids: string[] }>({
      query: (body) => ({
        url: 'assets/batch-delete',
        method: 'POST',
        body,
      }),
      invalidatesTags: [{ type: 'Asset', id: 'LIST' }],
    }),
  }),
});

export const {
  useGetAssetsQuery,
  useGetAssetByIdQuery,
  useCreateAssetMutation,
  useUpdateAssetMutation,
  useDeleteAssetMutation,
  useRunDepreciationMutation,
  useDisposeAssetMutation,
  useBatchUpdateAssetsMutation,
  useBatchDeleteAssetsMutation,
} = assetsApi;
