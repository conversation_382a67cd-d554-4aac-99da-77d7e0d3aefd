import { createApi } from '@reduxjs/toolkit/query/react';
import { Asset, AssetStatus } from '@/lib/types';
import { authenticatedBaseQuery } from '../baseQuery';

// Define the response type for paginated assets
export interface AssetsResponse {
  assets: Asset[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Define query parameters for assets list
export interface AssetsQueryParams {
  search?: string;
  status?: AssetStatus;
  depreciation_method?: string;
  purchase_date_from?: string;
  purchase_date_to?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Define the response type for the depreciation endpoint
interface DepreciationResponse {
  success: boolean;
  message: string;
  journal_entries_created_count: number;
}

// Define the response type for the dispose endpoint
interface DisposeAssetResponse {
  success: boolean;
  message: string;
  journal_entry_id: string;
}

export const assetsApi = createApi({
  reducerPath: 'assetsApi',
  baseQuery: authenticatedBaseQuery,
  tagTypes: ['Asset'],
  endpoints: (builder) => ({
    // Get assets with pagination and filtering
    getAssets: builder.query<AssetsResponse, AssetsQueryParams | void>({
      query: (params = {}) => {
        let url = 'assets';
        const queryParams = new URLSearchParams();

        // Add search parameter
        if (params.search) {
          queryParams.append('search', params.search);
        }

        // Add status filter
        if (params.status) {
          queryParams.append('status', params.status);
        }

        // Add depreciation method filter
        if (params.depreciation_method) {
          queryParams.append('depreciation_method', params.depreciation_method);
        }

        // Add date range filters
        if (params.purchase_date_from) {
          queryParams.append('purchase_date_from', params.purchase_date_from);
        }
        if (params.purchase_date_to) {
          queryParams.append('purchase_date_to', params.purchase_date_to);
        }

        // Add pagination parameters
        if (params.page) {
          queryParams.append('page', params.page.toString());
        }
        if (params.limit) {
          queryParams.append('limit', params.limit.toString());
        }

        // Add sorting parameters
        if (params.sortBy) {
          queryParams.append('sortBy', params.sortBy);
        }
        if (params.sortOrder) {
          queryParams.append('sortOrder', params.sortOrder);
        }

        // Append query string if there are parameters
        if (queryParams.toString()) {
          url += `?${queryParams.toString()}`;
        }

        return url;
      },
      // Transform the Go backend response to match frontend expectations
      transformResponse: (response: any): AssetsResponse => {
        // Transform individual asset objects from camelCase to snake_case
        const transformAsset = (asset: any): Asset => ({
          id: asset.id,
          restaurant_id: asset.branchId, // Map branchId to restaurant_id for compatibility
          name: asset.name,
          description: asset.description,
          purchase_date: asset.purchaseDate,
          purchase_cost: parseFloat(asset.purchasePrice || asset.currentValue || '0'),
          depreciation_method: asset.depreciationMethod,
          useful_life_months: asset.usefulLife ? asset.usefulLife * 12 : 0, // Convert years to months
          salvage_value: parseFloat(asset.salvageValue || '0'),
          asset_account_id: asset.assetAccountId || '',
          accumulated_depreciation_account_id: asset.accumulatedDepreciationAccountId || '',
          depreciation_expense_account_id: asset.depreciationExpenseAccountId || '',
          status: asset.status as AssetStatus,
          disposal_date: asset.disposalDate,
          disposal_proceeds: asset.disposalValue ? parseFloat(asset.disposalValue) : undefined,
          last_depreciation_date: asset.lastDepreciationDate,
          image_url: asset.imageUrl,
          created_at: asset.createdAt,
          updated_at: asset.updatedAt,
        });

        return {
          assets: (response.data || []).map(transformAsset),
          pagination: {
            total: response.total || 0,
            page: response.page || 1,
            limit: response.limit || 10,
            totalPages: response.totalPages || 0,
            hasNextPage: response.page < response.totalPages,
            hasPreviousPage: response.page > 1,
          },
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.assets.map(({ id }) => ({ type: 'Asset' as const, id })),
              { type: 'Asset', id: 'LIST' },
            ]
          : [{ type: 'Asset', id: 'LIST' }],
    }),

    // Get a specific asset by ID
    getAssetById: builder.query<Asset, string>({
      query: (id) => `assets/${id}`,
      transformResponse: (response: any): Asset => {
        // Transform individual asset object from camelCase to snake_case
        return {
          id: response.id,
          restaurant_id: response.branchId,
          name: response.name,
          description: response.description,
          purchase_date: response.purchaseDate,
          purchase_cost: parseFloat(response.purchasePrice || response.currentValue || '0'),
          depreciation_method: response.depreciationMethod,
          useful_life_months: response.usefulLife ? response.usefulLife * 12 : 0,
          salvage_value: parseFloat(response.salvageValue || '0'),
          asset_account_id: response.assetAccountId || '',
          accumulated_depreciation_account_id: response.accumulatedDepreciationAccountId || '',
          depreciation_expense_account_id: response.depreciationExpenseAccountId || '',
          status: response.status as AssetStatus,
          disposal_date: response.disposalDate,
          disposal_proceeds: response.disposalValue ? parseFloat(response.disposalValue) : undefined,
          last_depreciation_date: response.lastDepreciationDate,
          image_url: response.imageUrl,
          created_at: response.createdAt,
          updated_at: response.updatedAt,
        };
      },
      providesTags: (result, error, id) => [{ type: 'Asset', id }],
    }),

    // Create a new asset
    createAsset: builder.mutation<Asset, Partial<Asset>>({
      query: (body) => ({
        url: 'assets',
        method: 'POST',
        body,
      }),
      transformResponse: (response: any): Asset => {
        // Transform created asset response from camelCase to snake_case
        return {
          id: response.id,
          restaurant_id: response.branchId,
          name: response.name,
          description: response.description,
          purchase_date: response.purchaseDate,
          purchase_cost: parseFloat(response.purchasePrice || response.currentValue || '0'),
          depreciation_method: response.depreciationMethod,
          useful_life_months: response.usefulLife ? response.usefulLife * 12 : 0,
          salvage_value: parseFloat(response.salvageValue || '0'),
          asset_account_id: response.assetAccountId || '',
          accumulated_depreciation_account_id: response.accumulatedDepreciationAccountId || '',
          depreciation_expense_account_id: response.depreciationExpenseAccountId || '',
          status: response.status as AssetStatus,
          disposal_date: response.disposalDate,
          disposal_proceeds: response.disposalValue ? parseFloat(response.disposalValue) : undefined,
          last_depreciation_date: response.lastDepreciationDate,
          image_url: response.imageUrl,
          created_at: response.createdAt,
          updated_at: response.updatedAt,
        };
      },
      invalidatesTags: [{ type: 'Asset', id: 'LIST' }],
    }),

    // Update an asset
    updateAsset: builder.mutation<Asset, { id: string; body: Partial<Asset> }>({
      query: ({ id, body }) => ({
        url: `assets/${id}`,
        method: 'PUT',
        body,
      }),
      transformResponse: (response: any): Asset => {
        // Transform updated asset response from camelCase to snake_case
        return {
          id: response.id,
          restaurant_id: response.branchId,
          name: response.name,
          description: response.description,
          purchase_date: response.purchaseDate,
          purchase_cost: parseFloat(response.purchasePrice || response.currentValue || '0'),
          depreciation_method: response.depreciationMethod,
          useful_life_months: response.usefulLife ? response.usefulLife * 12 : 0,
          salvage_value: parseFloat(response.salvageValue || '0'),
          asset_account_id: response.assetAccountId || '',
          accumulated_depreciation_account_id: response.accumulatedDepreciationAccountId || '',
          depreciation_expense_account_id: response.depreciationExpenseAccountId || '',
          status: response.status as AssetStatus,
          disposal_date: response.disposalDate,
          disposal_proceeds: response.disposalValue ? parseFloat(response.disposalValue) : undefined,
          last_depreciation_date: response.lastDepreciationDate,
          image_url: response.imageUrl,
          created_at: response.createdAt,
          updated_at: response.updatedAt,
        };
      },
      invalidatesTags: (result, error, { id }) => [
        { type: 'Asset', id },
        { type: 'Asset', id: 'LIST' },
      ],
    }),

    // Delete an asset
    deleteAsset: builder.mutation<void, string>({
      query: (id) => ({
        url: `assets/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Asset', id },
        { type: 'Asset', id: 'LIST' },
      ],
    }),

    // Run depreciation
    runDepreciation: builder.mutation<DepreciationResponse, { run_until_date: string; asset_ids?: string[] }>({
      query: (body) => ({
        url: 'assets/depreciation',
        method: 'POST',
        body,
      }),
      invalidatesTags: [{ type: 'Asset', id: 'LIST' }],
    }),

    // Dispose/sell an asset
    disposeAsset: builder.mutation<DisposeAssetResponse, { id: string; disposal_date: string; disposal_proceeds: number }>({
      query: ({ id, ...body }) => ({
        url: `assets/${id}/dispose`,
        method: 'POST',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Asset', id },
        { type: 'Asset', id: 'LIST' },
      ],
    }),

    // Batch update assets
    batchUpdateAssets: builder.mutation<{ updated_count: number }, { asset_ids: string[]; status?: AssetStatus; [key: string]: any }>({
      query: (body) => ({
        url: 'assets/batch-update',
        method: 'POST',
        body,
      }),
      invalidatesTags: [{ type: 'Asset', id: 'LIST' }],
    }),

    // Batch delete assets
    batchDeleteAssets: builder.mutation<{ deleted_count: number }, { ids: string[] }>({
      query: (body) => ({
        url: 'assets/batch-delete',
        method: 'POST',
        body,
      }),
      invalidatesTags: [{ type: 'Asset', id: 'LIST' }],
    }),
  }),
});

export const {
  useGetAssetsQuery,
  useGetAssetByIdQuery,
  useCreateAssetMutation,
  useUpdateAssetMutation,
  useDeleteAssetMutation,
  useRunDepreciationMutation,
  useDisposeAssetMutation,
  useBatchUpdateAssetsMutation,
  useBatchDeleteAssetsMutation,
} = assetsApi;
