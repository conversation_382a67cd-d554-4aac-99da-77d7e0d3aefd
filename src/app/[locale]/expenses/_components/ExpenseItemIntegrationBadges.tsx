'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Icons
import {
  Link,
  Warehouse,
  UserCheck,
  Truck,
  Calculator,
  CreditCard,
  Users,
  Package,
  Building,
  ExternalLink,
  Plus,
  Target,
  FileText
} from 'lucide-react';

// Types
interface ExpenseItemIntegrationBadgesProps {
  expenseId: string;
  onManageIntegrations: () => void;
  compact?: boolean;
}

// Mock integration data - in real app this would come from API
interface ExpenseIntegration {
  id: string;
  expenseId: string;
  integrationType: string;
  entityId: string;
  entityName: string;
  metadata?: string;
}

const integrationIcons: Record<string, React.ReactNode> = {
  budget: <Target className="h-3 w-3" />,
  project: <Building className="h-3 w-3" />,
  employee: <UserCheck className="h-3 w-3" />,
  vendor: <Truck className="h-3 w-3" />,
  asset: <Package className="h-3 w-3" />,
  bank_account: <CreditCard className="h-3 w-3" />,
  tax_category: <Calculator className="h-3 w-3" />,
  bill: <FileText className="h-3 w-3" />,
  inventory: <Warehouse className="h-3 w-3" />,
  customer: <Users className="h-3 w-3" />,
};

const integrationLabels: Record<string, string> = {
  budget: 'Budget',
  project: 'Project',
  employee: 'Employee',
  vendor: 'Vendor',
  asset: 'Asset',
  bank_account: 'Bank Account',
  tax_category: 'Tax Category',
  bill: 'Bill/Invoice',
  inventory: 'Inventory',
  customer: 'Customer',
};

const integrationColors: Record<string, string> = {
  budget: 'hover:bg-blue-100 text-blue-700',
  project: 'hover:bg-green-100 text-green-700',
  employee: 'hover:bg-purple-100 text-purple-700',
  vendor: 'hover:bg-orange-100 text-orange-700',
  asset: 'hover:bg-indigo-100 text-indigo-700',
  bank_account: 'hover:bg-cyan-100 text-cyan-700',
  tax_category: 'hover:bg-red-100 text-red-700',
  bill: 'hover:bg-yellow-100 text-yellow-700',
  inventory: 'hover:bg-teal-100 text-teal-700',
  customer: 'hover:bg-pink-100 text-pink-700',
};

export function ExpenseItemIntegrationBadges({
  expenseId,
  onManageIntegrations,
  compact = false
}: ExpenseItemIntegrationBadgesProps) {
  // Mock data - in real app this would come from API query
  const isLoading = false;
  const integrations: ExpenseIntegration[] = [
    {
      id: '1',
      expenseId,
      integrationType: 'project',
      entityId: 'proj-1',
      entityName: 'Office Renovation Project',
      metadata: 'Phase 2 - Equipment Purchase'
    },
    {
      id: '2',
      expenseId,
      integrationType: 'budget',
      entityId: 'budget-1',
      entityName: 'Office Equipment Budget',
    }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center gap-1">
        <div className="h-5 w-16 bg-gray-200 rounded animate-pulse" />
      </div>
    );
  }

  const handleNavigateToEntity = (integration: ExpenseIntegration) => {
    // In real app, this would navigate to the specific entity
    console.log('Navigate to:', integration.integrationType, integration.entityId);
    
    // Example navigation logic:
    // const routes = {
    //   project: `/projects/${integration.entityId}`,
    //   budget: `/budget?item=${integration.entityId}`,
    //   employee: `/employees/${integration.entityId}`,
    //   vendor: `/vendors/${integration.entityId}`,
    //   asset: `/assets/${integration.entityId}`,
    //   bank_account: `/banking/accounts/${integration.entityId}`,
    //   tax_category: `/settings/tax-categories/${integration.entityId}`,
    //   bill: `/bills/${integration.entityId}`,
    // };
    // 
    // const route = routes[integration.integrationType];
    // if (route) {
    //   router.push(route);
    // }
  };

  if (compact) {
    return (
      <div className="flex items-center gap-1">
        {integrations.length > 0 ? (
          <>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge 
                    variant="secondary" 
                    className="cursor-pointer hover:bg-gray-200"
                    onClick={onManageIntegrations}
                  >
                    <Link className="h-3 w-3 mr-1" />
                    {integrations.length}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1">
                    <div className="font-medium">Linked to:</div>
                    {integrations.map((integration, index) => (
                      <div key={index} className="flex items-center gap-1 text-xs">
                        {integrationIcons[integration.integrationType]}
                        {integrationLabels[integration.integrationType]}
                      </div>
                    ))}
                    <div className="text-xs text-muted-foreground mt-1 pt-1 border-t">
                      Click to manage integrations
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </>
        ) : (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onManageIntegrations}
                  className="h-6 w-6 p-0 hover:bg-gray-100"
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-xs">Add integrations</div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center gap-1 flex-wrap">
      {integrations.map((integration, index) => (
        <TooltipProvider key={index}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge
                variant="secondary"
                className={`cursor-pointer transition-colors ${
                  integrationColors[integration.integrationType] || 'hover:bg-gray-200'
                }`}
                onClick={() => handleNavigateToEntity(integration)}
              >
                <div className="flex items-center gap-1">
                  {integrationIcons[integration.integrationType]}
                  <span className="text-xs">
                    {integrationLabels[integration.integrationType]}
                  </span>
                  <ExternalLink className="h-2 w-2 ml-1" />
                </div>
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1">
                <div className="font-medium">
                  {integrationLabels[integration.integrationType]} Integration
                </div>
                <div className="text-xs text-muted-foreground">
                  {integration.entityName}
                </div>
                {integration.metadata && (
                  <div className="text-xs text-muted-foreground">
                    {integration.metadata}
                  </div>
                )}
                <div className="text-xs text-muted-foreground mt-1 pt-1 border-t">
                  Click to view details
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ))}
      
      {/* Add Integration Button */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={onManageIntegrations}
              className="h-6 w-6 p-0 hover:bg-gray-100"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-xs">Add more integrations</div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
