'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';

// UI Components
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Icons
import {
  Link,
  Unlink,
  Warehouse,
  UserCheck,
  Truck,
  Calculator,
  CreditCard,
  Users,
  Package,
  Building,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  Target,
  FileText
} from 'lucide-react';

// Types
interface ExpenseItemIntegrationDialogProps {
  expenseId: string;
  expenseName: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  branchId?: string;
}

interface IntegrationOption {
  type: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  entityIdField: string;
  apiEndpoint: string;
  displayField: string;
}

const integrationOptions: IntegrationOption[] = [
  {
    type: 'budget',
    name: 'Budget Item',
    description: 'Link to specific budget items for tracking against planned expenses',
    icon: <Target className="h-4 w-4" />,
    category: 'Financial',
    entityIdField: 'budgetItemId',
    apiEndpoint: '/api/budget-items',
    displayField: 'accountName'
  },
  {
    type: 'project',
    name: 'Project',
    description: 'Link to projects for cost center tracking and project budgeting',
    icon: <Building className="h-4 w-4" />,
    category: 'Operational',
    entityIdField: 'projectId',
    apiEndpoint: '/api/projects',
    displayField: 'name'
  },
  {
    type: 'employee',
    name: 'Employee',
    description: 'Link to employees for reimbursement and expense reporting',
    icon: <UserCheck className="h-4 w-4" />,
    category: 'Operational',
    entityIdField: 'employeeId',
    apiEndpoint: '/api/employees',
    displayField: 'name'
  },
  {
    type: 'asset',
    name: 'Asset',
    description: 'Link to assets for maintenance and depreciation tracking',
    icon: <Package className="h-4 w-4" />,
    category: 'Operational',
    entityIdField: 'assetId',
    apiEndpoint: '/api/assets',
    displayField: 'name'
  },
  {
    type: 'vendor',
    name: 'Vendor',
    description: 'Enhanced vendor tracking with contract and payment terms',
    icon: <Truck className="h-4 w-4" />,
    category: 'Financial',
    entityIdField: 'vendorId',
    apiEndpoint: '/api/vendors',
    displayField: 'name'
  },
  {
    type: 'bank_account',
    name: 'Bank Account',
    description: 'Link to specific bank accounts for reconciliation',
    icon: <CreditCard className="h-4 w-4" />,
    category: 'Financial',
    entityIdField: 'bankAccountId',
    apiEndpoint: '/api/bank-accounts',
    displayField: 'accountName'
  },
  {
    type: 'tax_category',
    name: 'Tax Category',
    description: 'Link to tax categories for automated tax calculation',
    icon: <Calculator className="h-4 w-4" />,
    category: 'Compliance',
    entityIdField: 'taxCategoryId',
    apiEndpoint: '/api/tax-categories',
    displayField: 'name'
  },
  {
    type: 'bill',
    name: 'Bill/Invoice',
    description: 'Link to bills or invoices for payment tracking',
    icon: <FileText className="h-4 w-4" />,
    category: 'Financial',
    entityIdField: 'billId',
    apiEndpoint: '/api/bills',
    displayField: 'billNumber'
  }
];

// Mock integration data - in real app this would come from API
interface ExpenseIntegration {
  id: string;
  expenseId: string;
  integrationType: string;
  entityId: string;
  entityName: string;
  metadata?: string;
  createdAt: string;
}

export function ExpenseItemIntegrationDialog({
  expenseId,
  expenseName,
  open,
  onOpenChange,
  branchId
}: ExpenseItemIntegrationDialogProps) {
  const t = useTranslations('expenses.integration');
  
  // State
  const [selectedIntegrationType, setSelectedIntegrationType] = useState<string>('');
  const [selectedEntityId, setSelectedEntityId] = useState<string>('');
  const [metadata, setMetadata] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // Mock data - in real app this would come from API
  const [integrations, setIntegrations] = useState<ExpenseIntegration[]>([
    {
      id: '1',
      expenseId,
      integrationType: 'project',
      entityId: 'proj-1',
      entityName: 'Office Renovation Project',
      metadata: 'Phase 2 - Equipment Purchase',
      createdAt: new Date().toISOString()
    }
  ]);

  const [isLoading, setIsLoading] = useState(false);

  const handleCreateIntegration = async () => {
    if (!selectedIntegrationType || !selectedEntityId) {
      setError('Please select both integration type and entity');
      return;
    }

    try {
      setError(null);
      setIsLoading(true);
      
      // Mock API call - in real app this would be actual API call
      const newIntegration: ExpenseIntegration = {
        id: Date.now().toString(),
        expenseId,
        integrationType: selectedIntegrationType,
        entityId: selectedEntityId,
        entityName: `Mock ${selectedIntegrationType} Entity`,
        metadata: metadata || undefined,
        createdAt: new Date().toISOString()
      };

      setIntegrations(prev => [...prev, newIntegration]);
      
      // Reset form
      setSelectedIntegrationType('');
      setSelectedEntityId('');
      setMetadata('');
      
    } catch (err: any) {
      setError(err.message || 'Failed to create integration');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveIntegration = async (integrationId: string) => {
    try {
      setError(null);
      
      // Mock API call - in real app this would be actual API call
      setIntegrations(prev => prev.filter(i => i.id !== integrationId));
      
    } catch (err: any) {
      setError(err.message || 'Failed to remove integration');
    }
  };

  const getIntegrationIcon = (type: string) => {
    const option = integrationOptions.find(opt => opt.type === type);
    return option?.icon || <Link className="h-4 w-4" />;
  };

  const getIntegrationName = (type: string) => {
    const option = integrationOptions.find(opt => opt.type === type);
    return option?.name || type;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Link className="h-5 w-5" />
            Expense Item Integrations
          </DialogTitle>
          <DialogDescription>
            Link "{expenseName}" to other modules for enhanced expense tracking and reporting.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Existing Integrations */}
          {integrations.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-3">Current Integrations</h3>
              <div className="space-y-3">
                {integrations.map((integration) => (
                  <div
                    key={integration.id}
                    className="flex items-center justify-between p-3 border rounded-lg bg-muted/50"
                  >
                    <div className="flex items-center gap-3">
                      {getIntegrationIcon(integration.integrationType)}
                      <div>
                        <div className="font-medium">
                          {getIntegrationName(integration.integrationType)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {integration.entityName}
                        </div>
                        {integration.metadata && (
                          <div className="text-xs text-muted-foreground mt-1">
                            {integration.metadata}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <CheckCircle className="h-3 w-3" />
                        Linked
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveIntegration(integration.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Unlink className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <Separator />

          {/* Add New Integration */}
          <div>
            <h3 className="text-lg font-medium mb-3">Add New Integration</h3>
            <div className="space-y-4">
              {/* Integration Type Selection */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="integration-type">Integration Type</Label>
                  <Select
                    value={selectedIntegrationType}
                    onValueChange={setSelectedIntegrationType}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select integration type" />
                    </SelectTrigger>
                    <SelectContent>
                      {integrationOptions.map((option) => (
                        <SelectItem key={option.type} value={option.type}>
                          <div className="flex items-center gap-2">
                            {option.icon}
                            {option.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="entity-id">Entity ID</Label>
                  <Input
                    id="entity-id"
                    value={selectedEntityId}
                    onChange={(e) => setSelectedEntityId(e.target.value)}
                    placeholder="Enter entity ID"
                  />
                </div>
              </div>

              {/* Integration Description */}
              {selectedIntegrationType && (
                <div className="p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    {getIntegrationIcon(selectedIntegrationType)}
                    <span className="font-medium">
                      {getIntegrationName(selectedIntegrationType)}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {integrationOptions.find(opt => opt.type === selectedIntegrationType)?.description}
                  </p>
                </div>
              )}

              {/* Metadata */}
              <div>
                <Label htmlFor="metadata">Additional Notes (Optional)</Label>
                <Textarea
                  id="metadata"
                  value={metadata}
                  onChange={(e) => setMetadata(e.target.value)}
                  placeholder="Add any additional notes or metadata for this integration"
                  rows={3}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateIntegration}
                  disabled={!selectedIntegrationType || !selectedEntityId || isLoading}
                >
                  {isLoading ? 'Creating...' : 'Create Integration'}
                </Button>
              </div>
            </div>
          </div>

          {/* Integration Categories Info */}
          <div className="bg-muted/30 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Integration Categories</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <div className="font-medium text-blue-600 mb-1">Financial</div>
                <div className="text-muted-foreground">Budget tracking, vendor management, bank reconciliation</div>
              </div>
              <div>
                <div className="font-medium text-green-600 mb-1">Operational</div>
                <div className="text-muted-foreground">Project tracking, employee expenses, asset management</div>
              </div>
              <div>
                <div className="font-medium text-purple-600 mb-1">Compliance</div>
                <div className="text-muted-foreground">Tax categories, regulatory reporting</div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
