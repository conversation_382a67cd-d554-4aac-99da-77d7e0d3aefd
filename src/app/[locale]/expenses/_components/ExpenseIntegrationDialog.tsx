'use client';

import React, { useState, useMemo } from 'react';
import { useTranslations } from 'next-intl';

// UI Components
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Icons
import {
  Settings,
  DollarSign,
  FileText,
  Users,
  Package,
  Building,
  Bell,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Warehouse,
  UserCheck,
  Truck,
  Calculator,
  Banknote,
  CreditCard,
  <PERSON><PERSON><PERSON>,
  BarChart3,
  Zap,
  Shield
} from 'lucide-react';

// Types
interface ExpenseIntegration {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  enabled: boolean;
  category: 'financial' | 'operational' | 'alerts' | 'reporting' | 'advanced' | 'compliance';
  settings?: {
    threshold?: number;
    frequency?: string;
    autoApproval?: boolean;
    notifications?: boolean;
    reorderPoint?: number;
    forecastPeriod?: string;
    reconciliationMode?: string;
    taxCalculation?: string;
    cashFlowPrediction?: boolean;
  };
}

interface ExpenseIntegrationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  branchId?: string;
}

interface IntegrationCardProps {
  integration: ExpenseIntegration;
  onToggle: (id: string) => void;
  onUpdateSetting: (id: string, key: string, value: any) => void;
}

export function ExpenseIntegrationDialog({
  open,
  onOpenChange,
  branchId
}: ExpenseIntegrationDialogProps) {
  const t = useTranslations('expenses.integration');
  
  // State
  const [integrations, setIntegrations] = useState<ExpenseIntegration[]>([]);
  const [showHelp, setShowHelp] = useState(false);

  // Helper function to get default integrations
  const getDefaultIntegrations = (): ExpenseIntegration[] => [
    {
      id: 'budget',
      name: 'Budget Tracking',
      description: 'Monitor expenses against budget limits with real-time tracking',
      icon: <Target className="h-4 w-4" />,
      enabled: false,
      category: 'financial',
      settings: { threshold: 80, notifications: true }
    },
    {
      id: 'invoices',
      name: 'Invoice Matching',
      description: 'Automatically match expenses to related invoices and bills',
      icon: <FileText className="h-4 w-4" />,
      enabled: false,
      category: 'financial',
      settings: { threshold: 90, notifications: true }
    },
    {
      id: 'projects',
      name: 'Project Tracking',
      description: 'Link expenses to specific projects for cost center analysis',
      icon: <Building className="h-4 w-4" />,
      enabled: false,
      category: 'operational',
      settings: { notifications: true }
    },
    {
      id: 'employees',
      name: 'Employee Expenses',
      description: 'Track employee reimbursements and company card expenses',
      icon: <Users className="h-4 w-4" />,
      enabled: false,
      category: 'operational',
      settings: { autoApproval: false, notifications: true }
    },
    {
      id: 'assets',
      name: 'Asset Management',
      description: 'Link expenses to asset purchases and maintenance tracking',
      icon: <Package className="h-4 w-4" />,
      enabled: false,
      category: 'operational',
      settings: { notifications: true }
    },
    {
      id: 'vendors',
      name: 'Vendor Management',
      description: 'Enhanced vendor tracking with spending analysis and alerts',
      icon: <Truck className="h-4 w-4" />,
      enabled: false,
      category: 'operational',
      settings: { threshold: 85, notifications: true }
    },
    {
      id: 'alerts',
      name: 'Smart Alerts',
      description: 'Automated notifications for unusual spending patterns and approvals',
      icon: <Bell className="h-4 w-4" />,
      enabled: false,
      category: 'alerts',
      settings: { frequency: 'daily', notifications: true }
    },
    {
      id: 'analytics',
      name: 'Expense Analytics',
      description: 'Advanced reporting and spending pattern analysis',
      icon: <BarChart3 className="h-4 w-4" />,
      enabled: false,
      category: 'reporting',
      settings: { forecastPeriod: 'quarterly' }
    },
    {
      id: 'tax-management',
      name: 'Tax Management',
      description: 'Automated tax calculation, compliance tracking, and reporting',
      icon: <Calculator className="h-4 w-4" />,
      enabled: false,
      category: 'compliance',
      settings: {
        threshold: 100,
        frequency: 'monthly',
        autoApproval: false,
        notifications: true,
        taxCalculation: 'accrual' // accrual, cash, hybrid
      }
    },
    {
      id: 'banking-integration',
      name: 'Banking Integration',
      description: 'Real-time bank feed reconciliation and automated expense categorization',
      icon: <CreditCard className="h-4 w-4" />,
      enabled: false,
      category: 'compliance',
      settings: {
        threshold: 100,
        frequency: 'daily',
        autoApproval: true,
        notifications: true,
        reconciliationMode: 'automatic', // automatic, manual, hybrid
        cashFlowPrediction: true
      }
    }
  ];

  // Initialize integrations on first load
  React.useEffect(() => {
    if (integrations.length === 0) {
      setIntegrations(getDefaultIntegrations());
    }
  }, [integrations.length]);

  const toggleIntegration = (id: string) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === id 
        ? { ...integration, enabled: !integration.enabled }
        : integration
    ));
  };

  const updateIntegrationSetting = (id: string, key: string, value: any) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === id 
        ? { 
            ...integration, 
            settings: { ...integration.settings, [key]: value }
          }
        : integration
    ));
  };

  // Categorize integrations
  const categorizedIntegrations = useMemo(() => {
    return {
      financial: integrations.filter(i => i.category === 'financial'),
      operational: integrations.filter(i => i.category === 'operational'),
      alerts: integrations.filter(i => i.category === 'alerts'),
      reporting: integrations.filter(i => i.category === 'reporting'),
      advanced: integrations.filter(i => i.category === 'advanced'),
      compliance: integrations.filter(i => i.category === 'compliance'),
    };
  }, [integrations]);

  const enabledCount = integrations.filter(i => i.enabled).length;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              {showHelp ? 'Integration Guide' : 'Expense Integration Settings'}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowHelp(!showHelp)}
              className="text-xs"
            >
              {showHelp ? 'Back to Settings' : 'View Guide'}
            </Button>
          </DialogTitle>
          <DialogDescription>
            {showHelp ? (
              'Comprehensive guide to advanced expense integration features'
            ) : (
              <>
                Connect your expenses with other modules for comprehensive financial management.
                {enabledCount > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {enabledCount} integration{enabledCount !== 1 ? 's' : ''} enabled
                  </Badge>
                )}
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        {showHelp ? (
          <ExpenseIntegrationHelp />
        ) : (
          <div className="space-y-6">
            {/* Financial Modules */}
            <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Financial Modules
            </h3>
            <div className="space-y-4">
              {categorizedIntegrations.financial.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={toggleIntegration}
                  onUpdateSetting={updateIntegrationSetting}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Operational Modules */}
          <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <Building className="h-4 w-4" />
              Operational Modules
            </h3>
            <div className="space-y-4">
              {categorizedIntegrations.operational.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={toggleIntegration}
                  onUpdateSetting={updateIntegrationSetting}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Alerts & Notifications */}
          <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Alerts & Notifications
            </h3>
            <div className="space-y-4">
              {categorizedIntegrations.alerts.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={toggleIntegration}
                  onUpdateSetting={updateIntegrationSetting}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Reporting & Analytics */}
          <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Reporting & Analytics
            </h3>
            <div className="space-y-4">
              {categorizedIntegrations.reporting.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={toggleIntegration}
                  onUpdateSetting={updateIntegrationSetting}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Compliance & Financial Control */}
          <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Compliance & Financial Control
            </h3>
            <div className="space-y-4">
              {categorizedIntegrations.compliance.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={toggleIntegration}
                  onUpdateSetting={updateIntegrationSetting}
                />
              ))}
            </div>
          </div>

            {/* Warning Alert */}
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Enabling integrations will affect how expense data is processed and may require additional setup in connected modules.
              </AlertDescription>
            </Alert>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

function IntegrationCard({ integration, onToggle, onUpdateSetting }: IntegrationCardProps) {
  return (
    <div className={`border rounded-lg p-4 ${integration.enabled ? 'bg-muted/50' : ''}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3 flex-1">
          <div className="mt-1">
            {integration.icon}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium">{integration.name}</h4>
              {integration.enabled && (
                <CheckCircle className="h-4 w-4 text-green-600" />
              )}
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              {integration.description}
            </p>
            
            {/* Integration Settings */}
            {integration.enabled && integration.settings && (
              <div className="space-y-3 mt-3 pt-3 border-t">
                {integration.settings.threshold !== undefined && (
                  <div className="flex items-center gap-2">
                    <Label className="text-xs min-w-[80px]">Threshold:</Label>
                    <Input
                      type="number"
                      value={integration.settings.threshold}
                      onChange={(e) => onUpdateSetting(integration.id, 'threshold', parseInt(e.target.value))}
                      className="h-7 w-20 text-xs"
                      min="0"
                      max="100"
                    />
                    <span className="text-xs text-muted-foreground">%</span>
                  </div>
                )}
                
                {integration.settings.frequency !== undefined && (
                  <div className="flex items-center gap-2">
                    <Label className="text-xs min-w-[80px]">Frequency:</Label>
                    <Select
                      value={integration.settings.frequency}
                      onValueChange={(value) => onUpdateSetting(integration.id, 'frequency', value)}
                    >
                      <SelectTrigger className="h-7 w-24 text-xs">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                
                {integration.settings.autoApproval !== undefined && (
                  <div className="flex items-center gap-2">
                    <Label className="text-xs min-w-[80px]">Auto Approval:</Label>
                    <Switch
                      checked={integration.settings.autoApproval}
                      onCheckedChange={(checked) => onUpdateSetting(integration.id, 'autoApproval', checked)}
                    />
                  </div>
                )}
                
                {integration.settings.notifications !== undefined && (
                  <div className="flex items-center gap-2">
                    <Label className="text-xs min-w-[80px]">Notifications:</Label>
                    <Switch
                      checked={integration.settings.notifications}
                      onCheckedChange={(checked) => onUpdateSetting(integration.id, 'notifications', checked)}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        <Switch
          checked={integration.enabled}
          onCheckedChange={() => onToggle(integration.id)}
        />
      </div>
    </div>
  );
}

function ExpenseIntegrationHelp() {
  return (
    <div className="space-y-4 text-sm">
      <div>
        <h4 className="font-medium mb-2">Getting Started</h4>
        <p className="text-muted-foreground">
          Expense integrations allow you to connect your expense tracking with other business modules 
          for comprehensive financial management and automated workflows.
        </p>
      </div>
      
      <div>
        <h4 className="font-medium mb-2">Integration Categories</h4>
        <ul className="space-y-1 text-muted-foreground">
          <li>• <strong>Financial:</strong> Budget tracking, invoice matching, and financial controls</li>
          <li>• <strong>Operational:</strong> Project tracking, employee expenses, and asset management</li>
          <li>• <strong>Alerts:</strong> Smart notifications and approval workflows</li>
          <li>• <strong>Reporting:</strong> Advanced analytics and spending insights</li>
          <li>• <strong>Compliance:</strong> Tax management and banking integration</li>
        </ul>
      </div>
      
      <div>
        <h4 className="font-medium mb-2">Best Practices</h4>
        <ul className="space-y-1 text-muted-foreground">
          <li>• Start with budget tracking and invoice matching for immediate value</li>
          <li>• Enable notifications to stay informed of important expense activities</li>
          <li>• Set appropriate thresholds to balance automation with control</li>
          <li>• Review integration settings regularly as your business grows</li>
        </ul>
      </div>
    </div>
  );
}
