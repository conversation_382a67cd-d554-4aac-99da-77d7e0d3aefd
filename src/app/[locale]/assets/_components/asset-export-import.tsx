'use client';

import React, { useState, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Download, 
  Upload, 
  FileSpreadsheet, 
  FileText, 
  CheckCircle, 
  AlertCircle,
  Info
} from 'lucide-react';

interface AssetExportImportProps {
  assets: any[];
  onImportComplete: () => void;
}

export function AssetExportImport({ assets, onImportComplete }: AssetExportImportProps) {
  const t = useTranslations('assets.exportImport');
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importProgress, setImportProgress] = useState(0);
  const [isImporting, setIsImporting] = useState(false);
  const [importResults, setImportResults] = useState<{
    success: number;
    errors: string[];
    total: number;
  } | null>(null);

  // Export to CSV
  const exportToCSV = () => {
    if (assets.length === 0) {
      toast({
        title: 'No Data',
        description: 'No assets available to export.',
        variant: 'destructive',
      });
      return;
    }

    const headers = [
      'Name',
      'Description',
      'Purchase Date',
      'Purchase Cost',
      'Depreciation Method',
      'Useful Life (Months)',
      'Salvage Value',
      'Status',
      'Serial Number',
      'Location',
      'Notes'
    ];

    const csvContent = [
      headers.join(','),
      ...assets.map(asset => [
        `"${asset.name || ''}"`,
        `"${asset.description || ''}"`,
        asset.purchase_date || '',
        asset.purchase_cost || 0,
        asset.depreciation_method || '',
        asset.useful_life_months || 0,
        asset.salvage_value || 0,
        asset.status || '',
        `"${asset.serial_number || ''}"`,
        `"${asset.location || ''}"`,
        `"${asset.notes || ''}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `assets_export_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: 'Export Successful',
      description: `${assets.length} assets exported to CSV.`,
      variant: 'default',
    });
  };

  // Export to Excel (simplified JSON format)
  const exportToExcel = () => {
    if (assets.length === 0) {
      toast({
        title: 'No Data',
        description: 'No assets available to export.',
        variant: 'destructive',
      });
      return;
    }

    const excelData = assets.map(asset => ({
      Name: asset.name || '',
      Description: asset.description || '',
      'Purchase Date': asset.purchase_date || '',
      'Purchase Cost': asset.purchase_cost || 0,
      'Depreciation Method': asset.depreciation_method || '',
      'Useful Life (Months)': asset.useful_life_months || 0,
      'Salvage Value': asset.salvage_value || 0,
      Status: asset.status || '',
      'Serial Number': asset.serial_number || '',
      Location: asset.location || '',
      Notes: asset.notes || ''
    }));

    const jsonContent = JSON.stringify(excelData, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `assets_export_${new Date().toISOString().split('T')[0]}.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: 'Export Successful',
      description: `${assets.length} assets exported to JSON.`,
      variant: 'default',
    });
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
        setImportFile(file);
      } else {
        toast({
          title: 'Invalid File Type',
          description: 'Please select a CSV file.',
          variant: 'destructive',
        });
      }
    }
  };

  // Parse CSV content
  const parseCSV = (content: string): any[] => {
    const lines = content.split('\n');
    const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      if (lines[i].trim()) {
        const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
        const row: any = {};
        
        headers.forEach((header, index) => {
          const value = values[index] || '';
          switch (header.toLowerCase()) {
            case 'name':
              row.name = value;
              break;
            case 'description':
              row.description = value;
              break;
            case 'purchase date':
              row.purchase_date = value;
              break;
            case 'purchase cost':
              row.purchase_cost = parseFloat(value) || 0;
              break;
            case 'depreciation method':
              row.depreciation_method = value;
              break;
            case 'useful life (months)':
              row.useful_life_months = parseInt(value) || 0;
              break;
            case 'salvage value':
              row.salvage_value = parseFloat(value) || 0;
              break;
            case 'status':
              row.status = value;
              break;
            case 'serial number':
              row.serial_number = value;
              break;
            case 'location':
              row.location = value;
              break;
            case 'notes':
              row.notes = value;
              break;
          }
        });
        
        if (row.name) { // Only add rows with a name
          data.push(row);
        }
      }
    }

    return data;
  };

  // Handle import
  const handleImport = async () => {
    if (!importFile) return;

    setIsImporting(true);
    setImportProgress(0);
    setImportResults(null);

    try {
      const content = await importFile.text();
      const data = parseCSV(content);
      
      if (data.length === 0) {
        throw new Error('No valid data found in the CSV file.');
      }

      // Simulate import process with progress
      const results = { success: 0, errors: [] as string[], total: data.length };
      
      for (let i = 0; i < data.length; i++) {
        setImportProgress(((i + 1) / data.length) * 100);
        
        try {
          // Here you would call your API to create the asset
          // For now, we'll simulate the process
          await new Promise(resolve => setTimeout(resolve, 100));
          
          // Validate required fields
          if (!data[i].name) {
            throw new Error(`Row ${i + 2}: Name is required`);
          }
          if (!data[i].purchase_date) {
            throw new Error(`Row ${i + 2}: Purchase date is required`);
          }
          if (!data[i].purchase_cost || data[i].purchase_cost <= 0) {
            throw new Error(`Row ${i + 2}: Valid purchase cost is required`);
          }

          results.success++;
        } catch (error) {
          results.errors.push(error instanceof Error ? error.message : `Row ${i + 2}: Unknown error`);
        }
      }

      setImportResults(results);
      
      if (results.success > 0) {
        toast({
          title: 'Import Completed',
          description: `${results.success} assets imported successfully.`,
          variant: 'default',
        });
        onImportComplete();
      }

    } catch (error) {
      toast({
        title: 'Import Failed',
        description: error instanceof Error ? error.message : 'Failed to import assets.',
        variant: 'destructive',
      });
    } finally {
      setIsImporting(false);
    }
  };

  // Download template
  const downloadTemplate = () => {
    const headers = [
      'Name',
      'Description',
      'Purchase Date',
      'Purchase Cost',
      'Depreciation Method',
      'Useful Life (Months)',
      'Salvage Value',
      'Status',
      'Serial Number',
      'Location',
      'Notes'
    ];

    const sampleData = [
      'Sample Asset',
      'Sample description',
      '2024-01-01',
      '10000',
      'StraightLine',
      '60',
      '1000',
      'Active',
      'SN001',
      'Office',
      'Sample notes'
    ];

    const csvContent = [headers.join(','), sampleData.join(',')].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'assets_import_template.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: 'Template Downloaded',
      description: 'Import template downloaded successfully.',
      variant: 'default',
    });
  };

  return (
    <div className="flex space-x-2">
      {/* Export Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={exportToCSV}>
            <FileSpreadsheet className="mr-2 h-4 w-4" />
            Export to CSV
          </DropdownMenuItem>
          <DropdownMenuItem onClick={exportToExcel}>
            <FileText className="mr-2 h-4 w-4" />
            Export to JSON
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Import Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Import Assets</DialogTitle>
            <DialogDescription>
              Upload a CSV file to import multiple assets at once.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Template Download */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Download Template</CardTitle>
                <CardDescription>
                  Download a sample CSV template to ensure proper formatting.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" onClick={downloadTemplate} className="w-full">
                  <Download className="mr-2 h-4 w-4" />
                  Download Template
                </Button>
              </CardContent>
            </Card>

            {/* File Upload */}
            <div className="space-y-2">
              <Label htmlFor="file-upload">Select CSV File</Label>
              <Input
                id="file-upload"
                type="file"
                accept=".csv"
                onChange={handleFileSelect}
                ref={fileInputRef}
              />
              {importFile && (
                <p className="text-sm text-muted-foreground">
                  Selected: {importFile.name}
                </p>
              )}
            </div>

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <Label>Import Progress</Label>
                <Progress value={importProgress} className="w-full" />
                <p className="text-sm text-muted-foreground">
                  {Math.round(importProgress)}% complete
                </p>
              </div>
            )}

            {/* Import Results */}
            {importResults && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p><strong>Import Results:</strong></p>
                    <p>✅ Successfully imported: {importResults.success} assets</p>
                    {importResults.errors.length > 0 && (
                      <div>
                        <p>❌ Errors: {importResults.errors.length}</p>
                        <ul className="list-disc list-inside text-xs mt-1">
                          {importResults.errors.slice(0, 5).map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                          {importResults.errors.length > 5 && (
                            <li>... and {importResults.errors.length - 5} more errors</li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsImportDialogOpen(false);
                setImportFile(null);
                setImportResults(null);
                setImportProgress(0);
              }}
            >
              Close
            </Button>
            <Button
              onClick={handleImport}
              disabled={!importFile || isImporting}
            >
              {isImporting ? 'Importing...' : 'Import Assets'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
