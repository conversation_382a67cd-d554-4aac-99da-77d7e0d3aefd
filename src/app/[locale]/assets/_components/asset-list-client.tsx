'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useI18n } from '@/hooks/useI18n';
import { useRouter } from '@/i18n/navigation';
import {
  useGetAssetsQuery,
  useBatchUpdateAssetsMutation,
  useBatchDeleteAssetsMutation,
  useCreateAssetMutation,
  type AssetsQueryParams
} from '@/redux/services/assetsApi';
import { AssetStatus } from '@/lib/types';
import { Button } from '@/components/ui/button';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  Edit,
  Eye,
  Plus,
  Grid,
  List
} from 'lucide-react';
// Removed batch selection imports - using PaginatedTable's built-in selection

import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

// ✅ Import advanced pagination components
import { useAdvancedPagination } from '@/hooks/useAdvancedPagination';
import { PaginatedTable } from '@/components/common/PaginatedTable';
import { useAssetColumns } from '@/config/tableColumns';
import { AssetFormDialog } from './asset-form-dialog';
import { AssetIntegrationDialog } from './asset-integration-dialog';
import { useBranchContext } from '@/contexts/BranchContext';

export default function AssetListClient() {
  // Get translations
  const t = useTranslations('assets');
  const { formatCurrency, formatDate } = useI18n();
  const router = useRouter();

  // Get branch context
  const { selectedBranchId } = useBranchContext();

  // ✅ Replace manual state with advanced pagination
  const { state, actions, queryParams } = useAdvancedPagination({
    initialSort: {
      sortBy: 'purchase_date',
      sortOrder: 'desc'
    },
    initialLimit: 10,
    persist: true,
    persistKey: 'assets-page'
  });

  // Additional state for asset-specific features
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  // State for asset form modal
  const [isAssetFormOpen, setIsAssetFormOpen] = useState(false);
  const [editingAsset, setEditingAsset] = useState<any>(null);
  const [isIntegrationDialogOpen, setIsIntegrationDialogOpen] = useState(false);
  const [integrationAsset, setIntegrationAsset] = useState<any>(null);

  // State for batch action dialogs
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<AssetStatus | ''>('');
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // Modal cleanup function
  const cleanupModals = () => {
    // Force cleanup of any lingering modal overlays
    setTimeout(() => {
      const overlays = document.querySelectorAll('[data-radix-dialog-overlay], [data-radix-alert-dialog-overlay]');
      overlays.forEach(overlay => {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay);
        }
      });

      // Re-enable body interactions
      document.body.style.pointerEvents = '';
      document.body.style.overflow = '';
      document.body.classList.remove('overflow-hidden');
    }, 100);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupModals();
    };
  }, []);

  // Enhanced close handlers with cleanup
  const closeStatusDialog = () => {
    setIsStatusDialogOpen(false);
    setSelectedStatus('');
    cleanupModals();
  };

  // ✅ Build enhanced query parameters
  const enhancedQueryParams: AssetsQueryParams = {
    page: state.page,
    limit: state.limit,
    sortBy: state.sortBy,
    sortOrder: state.sortOrder,
    search: state.search || undefined,
  };

  // RTK Query hooks
  const { data: assetsResponse, isLoading: isLoadingAssets, error, isFetching } = useGetAssetsQuery(enhancedQueryParams);
  const [batchUpdateAssets] = useBatchUpdateAssetsMutation();
  const [batchDeleteAssets] = useBatchDeleteAssetsMutation();
  const [createAsset, { isLoading: isCreatingAsset }] = useCreateAssetMutation();
  const [updateAsset, { isLoading: isUpdatingAsset }] = useUpdateAssetMutation();
  const [deleteAsset, { isLoading: isDeletingAsset }] = useDeleteAssetMutation();
  const [runDepreciation, { isLoading: isRunningDepreciation }] = useRunDepreciationMutation();

  const { toast } = useToast();

  // Handle authentication errors
  useEffect(() => {
    if (error && 'status' in error && error.status === 401) {
      console.log('Authentication error detected, redirecting to login');
      toast({
        title: 'Authentication Required',
        description: 'Please log in to access this page',
        variant: 'destructive',
      });
      router.push('/login');
    }
  }, [error, router, toast]);

  // Extract assets and pagination from response
  const assets = assetsResponse?.assets || [];
  const pagination = assetsResponse?.pagination;

  // ✅ Asset action handlers for columns
  const handleView = (asset: any) => {
    router.push(`/assets/${asset.id}`);
  };

  const handleEdit = (asset: any) => {
    setEditingAsset(asset);
    setIsAssetFormOpen(true);
  };

  const handleDelete = async (asset: any) => {
    if (!confirm(`Are you sure you want to delete "${asset.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteAsset(asset.id).unwrap();
      toast({
        title: 'Success',
        description: `Asset "${asset.name}" deleted successfully`,
        variant: 'default',
      });
    } catch (error: any) {
      console.error('Failed to delete asset:', error);
      toast({
        title: 'Error',
        description: error.data?.error || 'Failed to delete asset',
        variant: 'destructive',
      });
    }
  };

  const handleDispose = (asset: any) => {
    router.push(`/assets/${asset.id}/dispose`);
  };

  const handleIntegration = (asset: any) => {
    setIntegrationAsset(asset);
    setIsIntegrationDialogOpen(true);
  };

  const handleRunDepreciation = async () => {
    if (!confirm('Are you sure you want to run depreciation for all assets? This will create journal entries.')) {
      return;
    }

    try {
      const today = new Date().toISOString();
      const result = await runDepreciation({
        run_until_date: today,
        asset_ids: selectedIds.length > 0 ? selectedIds : undefined // Run for selected assets or all
      }).unwrap();

      toast({
        title: 'Success',
        description: `Depreciation completed. ${result.journal_entries_created_count} journal entries created.`,
        variant: 'default',
      });

      // Clear selection after running depreciation
      setSelectedIds([]);
    } catch (error: any) {
      console.error('Failed to run depreciation:', error);
      toast({
        title: 'Error',
        description: error.data?.error || 'Failed to run depreciation',
        variant: 'destructive',
      });
    }
  };

  // Modal handlers
  const handleAddAsset = () => {
    setEditingAsset(null);
    setIsAssetFormOpen(true);
  };

  const handleAssetFormSubmit = async (formData: any) => {
    try {
      // Check if branch is selected
      if (!selectedBranchId) {
        toast({
          title: 'Error',
          description: 'Please select a branch first',
          variant: 'destructive',
        });
        throw new Error('No branch selected');
      }

      // Transform frontend data to backend format
      const backendData = {
        branchId: selectedBranchId, // Add required branchId
        name: formData.name,
        description: formData.description || null,
        purchaseDate: formData.purchase_date.toISOString(), // Convert Date to ISO string
        purchasePrice: formData.purchase_cost.toString(), // Convert number to string for decimal
        depreciationMethod: formData.depreciation_method, // Keep as is (StraightLine/None)
        usefulLife: formData.useful_life_months ? Math.round(formData.useful_life_months / 12) : null, // Convert months to years
        salvageValue: formData.salvage_value.toString(), // Convert number to string for decimal
        // Optional fields that might be added later
        serialNumber: null,
        location: null,
        custodianId: null,
        notes: null,
      };

      console.log('Submitting asset data:', backendData);

      if (editingAsset) {
        // Update existing asset
        await updateAsset({ id: editingAsset.id, body: backendData }).unwrap();
        toast({
          title: 'Success',
          description: 'Asset updated successfully',
          variant: 'default',
        });
      } else {
        await createAsset(backendData).unwrap();
        toast({
          title: 'Success',
          description: 'Asset created successfully',
          variant: 'default',
        });
      }
      setIsAssetFormOpen(false);
      setEditingAsset(null);
    } catch (error: any) {
      console.error('Failed to save asset:', error);
      toast({
        title: 'Error',
        description: error.data?.error || 'Failed to save asset',
        variant: 'destructive',
      });
      throw error; // Re-throw to let the form handle its loading state
    }
  };

  // ✅ Get column definitions
  const columns = useAssetColumns(
    handleEdit,
    handleDelete,
    handleView,
    handleDispose,
    true, // canEdit
    true  // canDelete
  );

  // Get status badge class
  const getStatusBadgeClass = (status: AssetStatus) => {
    switch (status) {
      case AssetStatus.Active:
        return 'bg-green-100 text-green-800';
      case AssetStatus.Disposed:
        return 'bg-gray-100 text-gray-800';
      case AssetStatus.Sold:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate book value (simplified)
  const calculateBookValue = (asset: any) => {
    const purchaseCost = asset.purchase_cost;
    const salvageValue = asset.salvage_value;
    const usefulLifeMonths = asset.useful_life_months || 1;

    // Calculate months since purchase
    const purchaseDate = new Date(asset.purchase_date);
    const today = new Date();
    const monthsSincePurchase = (today.getFullYear() - purchaseDate.getFullYear()) * 12 +
                               (today.getMonth() - purchaseDate.getMonth());

    // Calculate straight-line depreciation
    const depreciableAmount = purchaseCost - salvageValue;
    const monthlyDepreciation = depreciableAmount / usefulLifeMonths;
    const accumulatedDepreciation = Math.min(depreciableAmount, monthlyDepreciation * monthsSincePurchase);

    // Calculate book value
    return Math.max(purchaseCost - accumulatedDepreciation, salvageValue);
  };

  // Handle batch actions
  const handleBatchDelete = async () => {
    if (selectedIds.length === 0) {
      toast({
        title: 'Error',
        description: 'No assets selected',
        variant: 'destructive',
      });
      return;
    }

    try {
      const deleteResult = await batchDeleteAssets({ ids: selectedIds }).unwrap();
      toast({
        title: 'Success',
        description: `${deleteResult.deleted_count} asset(s) deleted successfully`,
        variant: 'default',
      });
      setSelectedIds([]);
    } catch (error) {
      console.error('Failed to delete assets:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete assets',
        variant: 'destructive',
      });
    }
  };

  const handleBatchStatusUpdate = () => {
    if (selectedIds.length === 0) {
      toast({
        title: 'Error',
        description: 'No assets selected',
        variant: 'destructive',
      });
      return;
    }
    setIsStatusDialogOpen(true);
  };

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!selectedStatus) return;

    try {
      const result = await batchUpdateAssets({
        asset_ids: selectedIds,
        status: selectedStatus as AssetStatus
      }).unwrap();

      toast({
        title: 'Success',
        description: `${result.updated_count} asset(s) updated successfully`,
        variant: 'default',
      });

      closeStatusDialog();
      setSelectedIds([]);
    } catch (error) {
      console.error('Failed to update asset status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update asset status',
        variant: 'destructive',
      });
    }
  };

  // Note: Pagination and sorting are now handled by useAdvancedPagination

  return (
    <div className="container mx-auto py-10">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <h1 className="text-3xl font-bold">{t('title')}</h1>
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
              {viewMode === 'grid' ? <List className="mr-2 h-4 w-4" /> : <Grid className="mr-2 h-4 w-4" />}
              {viewMode === 'grid' ? 'List View' : 'Grid View'}
            </Button>
            <Button
              variant="outline"
              onClick={handleRunDepreciation}
              disabled={isRunningDepreciation}
            >
              <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              {isRunningDepreciation ? 'Running...' : t('actions.runDepreciation')}
            </Button>
            <Button onClick={handleAddAsset}>
              <Plus className="mr-2 h-4 w-4" />
              {t('actions.addNewAsset')}
            </Button>
          </div>
        </div>

        {/* Filters removed - PaginatedTable handles search and filtering */}

        {/* Assets Table/Grid */}
        <div>
            {isLoadingAssets ? (
              <div className="space-y-2">
                {Array.from({ length: state.limit }).map((_, i) => (
                  <Skeleton key={i} className="h-10 w-full" />
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-500">{t('errors.loadingFailed')}</p>
                <Button variant="outline" onClick={() => window.location.reload()}>
                  {t('actions.retry')}
                </Button>
              </div>
            ) : viewMode === 'list' ? (
              // ✅ Replace entire table with PaginatedTable
              <PaginatedTable
                data={assets}
                columns={columns}
                paginationInfo={pagination}
                isLoading={isLoadingAssets}
                error={error}
                isFetching={isFetching}

                // Selection functionality
                selectable={true}
                selectedIds={selectedIds}
                onSelectionChange={setSelectedIds}

                // Search functionality
                searchable
                searchValue={state.search}
                onSearchChange={actions.handleSearch}
                searchPlaceholder="Search assets..."

                // Sorting
                sortBy={state.sortBy}
                sortOrder={state.sortOrder}
                onSortChange={actions.handleSort}

                // Pagination
                currentPage={state.page}
                pageSize={state.limit}
                onPageChange={actions.setPage}
                onPageSizeChange={actions.setLimit}

                // Display
                title="Assets"
                emptyMessage="No assets found"
              />
            ) : (
              // Grid view
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {assets.map(asset => (
                  <Card key={asset.id} className="overflow-hidden">
                    {asset.image_url ? (
                      <div className="w-full h-40 overflow-hidden">
                        <img
                          src={asset.image_url}
                          alt={asset.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = 'https://placehold.co/600x400?text=Asset';
                          }}
                        />
                      </div>
                    ) : (
                      <div className="w-full h-40 bg-muted flex items-center justify-center">
                        <div className="text-muted-foreground text-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                            <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                            <circle cx="9" cy="9" r="2" />
                            <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
                          </svg>
                          <p className="mt-2">{t('assetCard.noImage')}</p>
                        </div>
                      </div>
                    )}
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{asset.name}</CardTitle>
                          <CardDescription className="line-clamp-1">
                            {asset.description || "No description"}
                          </CardDescription>
                        </div>
                        <Badge className={getStatusBadgeClass(asset.status)}>
                          {asset.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-2 mb-4">
                        <div>
                          <p className="text-sm text-muted-foreground">{t('assetCard.purchaseCost')}</p>
                          <p className="font-medium">{formatCurrency(asset.purchase_cost)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">{t('assetCard.currentValue')}</p>
                          <p className="font-medium">{formatCurrency(calculateBookValue(asset))}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">{t('assetCard.purchaseDate')}</p>
                          <p className="font-medium">{formatDate(new Date(asset.purchase_date))}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">{t('assetCard.depreciation')}</p>
                          <p className="font-medium">{asset.depreciation_method}</p>
                        </div>
                      </div>

                      <div className="flex justify-end gap-2">
                        <Button variant="outline" size="sm" onClick={() => handleView(asset)}>
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleEdit(asset)}>
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleIntegration(asset)}>
                          <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                          </svg>
                          Integrate
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
        </div>

        {/* Manual pagination removed - PaginatedTable handles this automatically */}

        {/* Custom Batch Action Bar */}
        {selectedIds.length > 0 && (
          <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 bg-background border rounded-lg shadow-lg p-4 flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="font-medium">
                {selectedIds.length} asset{selectedIds.length !== 1 ? 's' : ''} selected
              </span>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={handleBatchStatusUpdate}
                className="px-3 py-1 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90"
              >
                Update Status
              </button>
              <button
                onClick={handleBatchDelete}
                className="px-3 py-1 bg-destructive text-destructive-foreground rounded-md text-sm hover:bg-destructive/90"
              >
                Delete Selected
              </button>
              <button
                onClick={() => setSelectedIds([])}
                className="px-3 py-1 bg-secondary text-secondary-foreground rounded-md text-sm hover:bg-secondary/90"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Status Update Dialog */}
        <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Update Asset Status</DialogTitle>
              <DialogDescription>
                Update the status for {selectedIds.length} selected asset{selectedIds.length !== 1 ? 's' : ''}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as AssetStatus | '')}>
                  <SelectTrigger id="status">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(AssetStatus).map(status => (
                      <SelectItem key={status} value={status}>
                        {status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={closeStatusDialog}>
                Cancel
              </Button>
              <Button onClick={handleStatusUpdate}>
                Update
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Asset Form Dialog */}
        <AssetFormDialog
          open={isAssetFormOpen}
          onOpenChange={setIsAssetFormOpen}
          asset={editingAsset}
          onSubmit={handleAssetFormSubmit}
          isSubmitting={isCreatingAsset}
        />

        {/* Asset Integration Dialog */}
        <AssetIntegrationDialog
          open={isIntegrationDialogOpen}
          onOpenChange={setIsIntegrationDialogOpen}
          asset={integrationAsset}
        />
    </div>
  );
}