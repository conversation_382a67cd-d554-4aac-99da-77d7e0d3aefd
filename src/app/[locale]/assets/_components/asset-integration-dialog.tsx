'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { Asset } from '@/lib/types';
import { 
  Calculator, 
  DollarSign, 
  Package, 
  Users, 
  Building, 
  FileText,
  TrendingUp,
  Calendar,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface AssetIntegrationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  asset: Asset | null;
}

interface IntegrationModule {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  status: 'available' | 'connected' | 'unavailable';
  benefits: string[];
  actions: {
    label: string;
    action: () => void;
  }[];
}

export function AssetIntegrationDialog({
  open,
  onOpenChange,
  asset
}: AssetIntegrationDialogProps) {
  const t = useTranslations('assets.integration');
  const { toast } = useToast();
  const [isConnecting, setIsConnecting] = useState<string | null>(null);

  // Define available integration modules
  const integrationModules: IntegrationModule[] = [
    {
      id: 'budget',
      name: 'Budget Management',
      description: 'Connect asset depreciation to budget planning and forecasting',
      icon: <Calculator className="h-5 w-5" />,
      status: 'available',
      benefits: [
        'Automatic depreciation budget allocation',
        'Asset replacement planning',
        'Capital expenditure forecasting',
        'Monthly depreciation tracking'
      ],
      actions: [
        {
          label: 'Connect to Budget',
          action: () => handleConnect('budget')
        }
      ]
    },
    {
      id: 'inventory',
      name: 'Inventory Management',
      description: 'Link assets to inventory items and stock management',
      icon: <Package className="h-5 w-5" />,
      status: 'available',
      benefits: [
        'Track asset-related inventory',
        'Maintenance parts management',
        'Asset utilization tracking',
        'Replacement parts forecasting'
      ],
      actions: [
        {
          label: 'Connect to Inventory',
          action: () => handleConnect('inventory')
        }
      ]
    },
    {
      id: 'projects',
      name: 'Project Management',
      description: 'Assign assets to projects and track utilization',
      icon: <Building className="h-5 w-5" />,
      status: 'available',
      benefits: [
        'Project asset allocation',
        'Cost center tracking',
        'Asset utilization reports',
        'Project profitability analysis'
      ],
      actions: [
        {
          label: 'Assign to Project',
          action: () => handleConnect('projects')
        }
      ]
    },
    {
      id: 'maintenance',
      name: 'Maintenance Scheduling',
      description: 'Schedule and track asset maintenance activities',
      icon: <Calendar className="h-5 w-5" />,
      status: 'available',
      benefits: [
        'Preventive maintenance scheduling',
        'Maintenance cost tracking',
        'Asset downtime monitoring',
        'Service history management'
      ],
      actions: [
        {
          label: 'Setup Maintenance',
          action: () => handleConnect('maintenance')
        }
      ]
    },
    {
      id: 'reporting',
      name: 'Financial Reporting',
      description: 'Include assets in financial reports and analytics',
      icon: <TrendingUp className="h-5 w-5" />,
      status: 'connected',
      benefits: [
        'Balance sheet integration',
        'Depreciation reports',
        'Asset performance analytics',
        'Tax reporting compliance'
      ],
      actions: [
        {
          label: 'View Reports',
          action: () => handleConnect('reporting')
        }
      ]
    },
    {
      id: 'insurance',
      name: 'Insurance Management',
      description: 'Track asset insurance coverage and claims',
      icon: <FileText className="h-5 w-5" />,
      status: 'unavailable',
      benefits: [
        'Insurance coverage tracking',
        'Claims management',
        'Premium optimization',
        'Risk assessment'
      ],
      actions: [
        {
          label: 'Coming Soon',
          action: () => {}
        }
      ]
    }
  ];

  const handleConnect = async (moduleId: string) => {
    if (!asset) return;
    
    setIsConnecting(moduleId);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast({
        title: 'Integration Successful',
        description: `Asset "${asset.name}" has been connected to ${moduleId} module.`,
        variant: 'default',
      });
      
      // Here you would typically make an API call to create the integration
      console.log(`Connecting asset ${asset.id} to ${moduleId} module`);
      
    } catch (error) {
      toast({
        title: 'Integration Failed',
        description: 'Failed to connect to the module. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsConnecting(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Connected</Badge>;
      case 'available':
        return <Badge variant="secondary">Available</Badge>;
      case 'unavailable':
        return <Badge variant="outline" className="text-gray-500"><AlertCircle className="h-3 w-3 mr-1" />Coming Soon</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  if (!asset) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] flex flex-col p-0">
        <DialogHeader className="p-6 pb-2">
          <DialogTitle>Asset Integrations</DialogTitle>
          <DialogDescription>
            Connect "{asset.name}" with other modules to enhance functionality and automate workflows.
          </DialogDescription>
        </DialogHeader>

        <div className="overflow-y-auto px-6 py-2 flex-1">
          <Tabs defaultValue="available" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="available">Available</TabsTrigger>
              <TabsTrigger value="connected">Connected</TabsTrigger>
              <TabsTrigger value="all">All Modules</TabsTrigger>
            </TabsList>
            
            <TabsContent value="available" className="space-y-4 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {integrationModules
                  .filter(module => module.status === 'available')
                  .map((module) => (
                    <IntegrationCard 
                      key={module.id} 
                      module={module} 
                      isConnecting={isConnecting === module.id}
                    />
                  ))}
              </div>
            </TabsContent>
            
            <TabsContent value="connected" className="space-y-4 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {integrationModules
                  .filter(module => module.status === 'connected')
                  .map((module) => (
                    <IntegrationCard 
                      key={module.id} 
                      module={module} 
                      isConnecting={isConnecting === module.id}
                    />
                  ))}
              </div>
            </TabsContent>
            
            <TabsContent value="all" className="space-y-4 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {integrationModules.map((module) => (
                  <IntegrationCard 
                    key={module.id} 
                    module={module} 
                    isConnecting={isConnecting === module.id}
                  />
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="p-6 pt-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Separate component for integration cards
function IntegrationCard({ 
  module, 
  isConnecting 
}: { 
  module: IntegrationModule; 
  isConnecting: boolean;
}) {
  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {module.icon}
            <CardTitle className="text-lg">{module.name}</CardTitle>
          </div>
          {getStatusBadge(module.status)}
        </div>
        <CardDescription>{module.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div>
            <h4 className="text-sm font-medium mb-2">Benefits:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              {module.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  {benefit}
                </li>
              ))}
            </ul>
          </div>
          
          <div className="pt-2">
            {module.actions.map((action, index) => (
              <Button
                key={index}
                onClick={action.action}
                disabled={module.status === 'unavailable' || isConnecting}
                className="w-full"
                variant={module.status === 'connected' ? 'outline' : 'default'}
              >
                {isConnecting ? 'Connecting...' : action.label}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Helper function for status badges (moved outside component to avoid recreation)
function getStatusBadge(status: string) {
  switch (status) {
    case 'connected':
      return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Connected</Badge>;
    case 'available':
      return <Badge variant="secondary">Available</Badge>;
    case 'unavailable':
      return <Badge variant="outline" className="text-gray-500"><AlertCircle className="h-3 w-3 mr-1" />Coming Soon</Badge>;
    default:
      return <Badge variant="outline">Unknown</Badge>;
  }
}
