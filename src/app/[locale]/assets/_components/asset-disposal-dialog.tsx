'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { format } from 'date-fns';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

import { Asset } from '@/lib/types';

// Define the form schema using Zod
const formSchema = z.object({
  disposal_date: z.date({ required_error: "Disposal date is required." }),
  disposal_proceeds: z.coerce.number().min(0, { message: "Disposal proceeds must be a positive number." }),
  disposal_method: z.enum(['Sold', 'Scrapped', 'Donated', 'Stolen', 'Lost']),
  notes: z.string().optional(),
});

// Define the form data type
type FormData = z.infer<typeof formSchema>;

interface AssetDisposalDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  asset: Asset | null;
  onSubmit: (assetId: string, data: FormData) => Promise<void>;
  isSubmitting: boolean;
}

export function AssetDisposalDialog({
  open,
  onOpenChange,
  asset,
  onSubmit,
  isSubmitting
}: AssetDisposalDialogProps) {
  // Get translations
  const t = useTranslations('assets.disposalDialog');
  const [error, setError] = useState<string | null>(null);

  // Calculate book value (simplified for display)
  const calculateBookValue = (asset: Asset | null) => {
    if (!asset) return 0;

    // In a real app, you would calculate accumulated depreciation
    // For now, we'll use a simplified approach
    const purchaseCost = asset.purchase_cost;
    const salvageValue = asset.salvage_value;
    const usefulLifeMonths = asset.useful_life_months || 1;

    // Calculate months since purchase
    const purchaseDate = new Date(asset.purchase_date);
    const today = new Date();
    const monthsSincePurchase = (today.getFullYear() - purchaseDate.getFullYear()) * 12 +
                               (today.getMonth() - purchaseDate.getMonth());

    // Calculate straight-line depreciation
    const depreciableAmount = purchaseCost - salvageValue;
    const monthlyDepreciation = depreciableAmount / usefulLifeMonths;
    const accumulatedDepreciation = Math.min(depreciableAmount, monthlyDepreciation * monthsSincePurchase);

    // Calculate book value
    return Math.max(purchaseCost - accumulatedDepreciation, salvageValue);
  };

  // Initialize the form
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      disposal_date: new Date(),
      disposal_proceeds: 0,
      disposal_method: 'Sold',
      notes: '',
    },
  });

  // Handle form submission
  const handleSubmit = async (data: FormData) => {
    if (!asset) return;

    try {
      setError(null);
      await onSubmit(asset.id, data);
      onOpenChange(false);
    } catch (error) {
      console.error('Error disposing asset:', error);
      setError(error instanceof Error ? error.message : 'An error occurred while disposing the asset');
    }
  };

  // Calculate estimated book value
  const bookValue = asset ? calculateBookValue(asset) : 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] flex flex-col p-0">
        <DialogHeader className="p-6 pb-2">
          <DialogTitle>{t('title')}</DialogTitle>
          <DialogDescription>
            {t('description')}
          </DialogDescription>
        </DialogHeader>

        <div className="overflow-y-auto px-6 py-2 flex-1">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {asset && (
            <div className="bg-muted p-4 rounded-md mb-4">
              <h3 className="font-medium">{asset.name}</h3>
              <div className="grid grid-cols-2 gap-2 mt-2 text-sm">
                <div>
                  <p className="text-muted-foreground">{t('form.purchaseCost')}:</p>
                  <p>${asset.purchase_cost.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">{t('form.estimatedBookValue')}:</p>
                  <p>${bookValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">{t('form.purchaseDate')}:</p>
                  <p>{new Date(asset.purchase_date).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          )}

          <Form {...form}>
            <form id="disposal-form" onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 gap-4">
                {/* Disposal Date */}
                <FormField
                  control={form.control}
                  name="disposal_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>{t('form.disposalDate')}</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>{t('form.pickDate')}</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date > new Date() || date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Disposal Proceeds */}
                <FormField
                  control={form.control}
                  name="disposal_proceeds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Disposal Proceeds</FormLabel>
                      <FormControl>
                        <Input type="number" min="0" step="0.01" {...field} />
                      </FormControl>
                      <FormDescription>
                        Amount received from selling or disposing of the asset.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Disposal Method */}
                <FormField
                  control={form.control}
                  name="disposal_method"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Disposal Method</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select disposal method" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Sold">Sold</SelectItem>
                          <SelectItem value="Scrapped">Scrapped</SelectItem>
                          <SelectItem value="Donated">Donated</SelectItem>
                          <SelectItem value="Stolen">Stolen</SelectItem>
                          <SelectItem value="Lost">Lost</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Notes */}
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter any additional notes about the disposal..."
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Gain/Loss Calculation */}
              {asset && (
                <div className="border rounded-md p-3">
                  <h4 className="font-medium mb-2">{t('form.gainLossCalculation')}</h4>
                  <div className="grid grid-cols-2 gap-x-4 text-sm">
                    <div className="text-muted-foreground">{t('form.disposalProceeds')}:</div>
                    <div>${parseFloat(form.watch('disposal_proceeds').toString() || '0').toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</div>

                    <div className="text-muted-foreground">{t('form.bookValue')}:</div>
                    <div>${bookValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</div>

                    <div className="text-muted-foreground font-medium mt-1">{t('form.gainLoss')}:</div>
                    <div className={cn(
                      "font-medium mt-1",
                      parseFloat(form.watch('disposal_proceeds').toString() || '0') - bookValue > 0
                        ? "text-green-600"
                        : parseFloat(form.watch('disposal_proceeds').toString() || '0') - bookValue < 0
                          ? "text-red-600"
                          : ""
                    )}>
                      ${(parseFloat(form.watch('disposal_proceeds').toString() || '0') - bookValue).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </div>
                  </div>
                </div>
              )}
            </form>
          </Form>
        </div>

        <DialogFooter className="p-6 pt-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
            form="disposal-form"
          >
            {t('buttons.cancel')}
          </Button>
          <Button type="submit" disabled={isSubmitting} form="disposal-form">
            {isSubmitting ? t('buttons.saving') : t('buttons.dispose')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
