'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { format } from 'date-fns';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { FileUpload } from "@/components/ui/file-upload";

import { Asset, AssetStatus } from '@/lib/types';
import { useGetAccountsQuery, useBulkCreateAccountsMutation } from '@/redux/services/chartOfAccountsApi';
import { useBranchContext } from '@/contexts/BranchContext';
import { useToast } from '@/components/ui/use-toast';

// Define the form schema using Zod
const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  description: z.string().optional(),
  purchase_date: z.date({ required_error: "Purchase date is required." }),
  purchase_cost: z.coerce.number().min(0, { message: "Purchase cost must be a positive number." }),
  depreciation_method: z.enum(['None', 'StraightLine']),
  useful_life_months: z.coerce.number().min(0).optional(),
  salvage_value: z.coerce.number().min(0, { message: "Salvage value must be a positive number." }),
  asset_account_id: z.string().min(1, { message: "Asset account is required." }),
  accumulated_depreciation_account_id: z.string().min(1, { message: "Accumulated depreciation account is required." }),
  depreciation_expense_account_id: z.string().min(1, { message: "Depreciation expense account is required." }),
  image_url: z.string().optional(),
});

// Define the form data type
type FormData = z.infer<typeof formSchema>;

interface AssetFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  asset?: Asset | null;
  onSubmit: (data: FormData) => Promise<void>;
  isSubmitting: boolean;
}

export function AssetFormDialog({
  open,
  onOpenChange,
  asset,
  onSubmit,
  isSubmitting
}: AssetFormDialogProps) {
  // Get translations
  const t = useTranslations('assets.formDialog');

  // Get branch context and toast
  const { selectedBranchId } = useBranchContext();
  const { toast } = useToast();

  // RTK Query hooks
  const { data, isLoading: isLoadingAccounts } = useGetAccountsQuery();
  const [bulkCreateAccounts, { isLoading: isCreatingAccounts }] = useBulkCreateAccountsMutation();

  // Extract accounts array from the response
  const accounts = data?.data || [];

  // Filter accounts by type - only when accounts is a valid array
  const assetAccounts = React.useMemo(() => {
    if (!Array.isArray(accounts)) return [];
    return accounts.filter(acc => {
      // Handle both backend format (type, isActive) and legacy format (account_type, is_active)
      const accountType = acc.type || acc.account_type;
      const isActive = acc.isActive !== undefined ? acc.isActive : acc.is_active;
      return accountType && accountType.includes('Asset') && isActive;
    });
  }, [accounts]);

  const expenseAccounts = React.useMemo(() => {
    if (!Array.isArray(accounts)) return [];
    return accounts.filter(acc => {
      // Handle both backend format (type, isActive) and legacy format (account_type, is_active)
      const accountType = acc.type || acc.account_type;
      const isActive = acc.isActive !== undefined ? acc.isActive : acc.is_active;
      return accountType && accountType.includes('Expense') && isActive;
    });
  }, [accounts]);

  // Auto-create essential asset accounts
  const createEssentialAssetAccounts = async () => {
    if (!selectedBranchId) {
      console.error('No branch selected');
      return;
    }

    const essentialAccounts = [
      {
        code: "1500",
        name: "Equipment",
        type: "FixedAsset",
        description: "Office and business equipment"
      },
      {
        code: "1510",
        name: "Furniture and Fixtures",
        type: "FixedAsset",
        description: "Office furniture and fixtures"
      },
      {
        code: "1520",
        name: "Vehicles",
        type: "FixedAsset",
        description: "Company vehicles"
      },
      {
        code: "1590",
        name: "Accumulated Depreciation - Equipment",
        type: "FixedAsset",
        description: "Accumulated depreciation for equipment (contra-asset)"
      },
      {
        code: "1591",
        name: "Accumulated Depreciation - Furniture",
        type: "FixedAsset",
        description: "Accumulated depreciation for furniture (contra-asset)"
      },
      {
        code: "1592",
        name: "Accumulated Depreciation - Vehicles",
        type: "FixedAsset",
        description: "Accumulated depreciation for vehicles (contra-asset)"
      },
      {
        code: "5260",
        name: "Depreciation Expense",
        type: "OperatingExpense",
        description: "Depreciation expense for fixed assets"
      }
    ];

    try {
      const result = await bulkCreateAccounts({
        branchId: selectedBranchId,
        accounts: essentialAccounts
      }).unwrap();

      toast({
        title: 'Success',
        description: `Created ${result.createdCount} essential asset accounts successfully`,
        variant: 'default',
      });

      console.log(`Created ${result.createdCount} accounts successfully`);
      if (result.errors && result.errors.length > 0) {
        console.log('Some accounts already exist:', result.errors);
      }
    } catch (error) {
      console.error('Failed to create accounts:', error);
      toast({
        title: 'Error',
        description: 'Failed to create accounts. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Initialize the form
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      purchase_date: new Date(),
      purchase_cost: 0,
      depreciation_method: 'StraightLine',
      useful_life_months: 60, // Default to 5 years
      salvage_value: 0,
      asset_account_id: '',
      accumulated_depreciation_account_id: '',
      depreciation_expense_account_id: '',
      image_url: '',
    },
  });

  // Update form values when asset changes
  useEffect(() => {
    if (asset) {
      form.reset({
        name: asset.name,
        description: asset.description || '',
        purchase_date: new Date(asset.purchase_date),
        purchase_cost: asset.purchase_cost,
        depreciation_method: asset.depreciation_method as 'None' | 'StraightLine',
        useful_life_months: asset.useful_life_months,
        salvage_value: asset.salvage_value,
        asset_account_id: asset.asset_account_id,
        accumulated_depreciation_account_id: asset.accumulated_depreciation_account_id,
        depreciation_expense_account_id: asset.depreciation_expense_account_id,
        image_url: asset.image_url || '',
      });
    } else {
      form.reset({
        name: '',
        description: '',
        purchase_date: new Date(),
        purchase_cost: 0,
        depreciation_method: 'StraightLine',
        useful_life_months: 60,
        salvage_value: 0,
        asset_account_id: '',
        accumulated_depreciation_account_id: '',
        depreciation_expense_account_id: '',
        image_url: '',
      });
    }
  }, [asset, form]);

  // Handle form submission
  const handleSubmit = async (data: FormData) => {
    try {
      // Log the form data to see what's being submitted
      console.log('Submitting form data:', data);

      // Ensure image_url is included in the submission
      const imageUrl = form.getValues('image_url');
      console.log('Image URL from form.getValues():', imageUrl);

      // Create a copy of the data with the image URL explicitly included
      const submissionData = {
        ...data,
        image_url: imageUrl || data.image_url || '',
      };

      console.log('Final submission data with image URL:', submissionData);
      await onSubmit(submissionData);
      onOpenChange(false);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col p-0">
        <DialogHeader className="p-6 pb-2">
          <DialogTitle>{asset ? t('editTitle') : t('addTitle')}</DialogTitle>
          <DialogDescription>
            {asset
              ? t('editDescription')
              : t('addDescription')}
          </DialogDescription>
        </DialogHeader>

        <div className="overflow-y-auto px-6 py-2 flex-1">
          <Form {...form}>
            <form id="asset-form" onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 gap-4">
                {/* Name */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.name')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('form.namePlaceholder')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Description */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.description')}</FormLabel>
                      <FormControl>
                        <Textarea placeholder={t('form.descriptionPlaceholder')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Purchase Date */}
                <FormField
                  control={form.control}
                  name="purchase_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>{t('form.purchaseDate')}</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>{t('form.pickDate')}</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date > new Date() || date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  {/* Purchase Cost */}
                  <FormField
                    control={form.control}
                    name="purchase_cost"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.purchaseCost')}</FormLabel>
                        <FormControl>
                          <Input type="number" min="0" step="0.01" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Salvage Value */}
                  <FormField
                    control={form.control}
                    name="salvage_value"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.salvageValue')}</FormLabel>
                        <FormControl>
                          <Input type="number" min="0" step="0.01" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {/* Depreciation Method */}
                  <FormField
                    control={form.control}
                    name="depreciation_method"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.depreciationMethod')}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('form.selectMethod')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="None">{t('form.none')}</SelectItem>
                            <SelectItem value="StraightLine">{t('form.straightLine')}</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Useful Life Months */}
                  <FormField
                    control={form.control}
                    name="useful_life_months"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.usefulLife')}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="1"
                            {...field}
                            disabled={form.watch('depreciation_method') === 'None'}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Auto-create accounts button */}
                {(assetAccounts.length === 0 || expenseAccounts.length === 0) && !isLoadingAccounts && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-yellow-800">Missing Asset Accounts</h4>
                        <p className="text-sm text-yellow-700">
                          {assetAccounts.length === 0 && "No asset accounts found. "}
                          {expenseAccounts.length === 0 && "No expense accounts found. "}
                          Create essential accounts for asset management.
                        </p>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={createEssentialAssetAccounts}
                        disabled={isCreatingAccounts}
                        className="ml-4"
                      >
                        {isCreatingAccounts ? "Creating..." : "Auto-Create Accounts"}
                      </Button>
                    </div>
                  </div>
                )}

                {/* Asset Account */}
                {isLoadingAccounts ? (
                  <FormItem>
                    <FormLabel>{t('form.assetAccount')}</FormLabel>
                    <Skeleton className="h-10 w-full" />
                  </FormItem>
                ) : (
                  <FormField
                    control={form.control}
                    name="asset_account_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.assetAccount')}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('form.selectAssetAccount')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {assetAccounts.map((account) => (
                              <SelectItem key={account.id} value={account.id}>
                                {(account.code || account.account_code)} - {(account.name || account.account_name)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Accumulated Depreciation Account */}
                {isLoadingAccounts ? (
                  <FormItem>
                    <FormLabel>{t('form.accumulatedDepreciation')}</FormLabel>
                    <Skeleton className="h-10 w-full" />
                  </FormItem>
                ) : (
                  <FormField
                    control={form.control}
                    name="accumulated_depreciation_account_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.accumulatedDepreciation')}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('form.selectAccumulatedDepreciation')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {assetAccounts.map((account) => (
                              <SelectItem key={account.id} value={account.id}>
                                {(account.code || account.account_code)} - {(account.name || account.account_name)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Depreciation Expense Account */}
                {isLoadingAccounts ? (
                  <FormItem>
                    <FormLabel>{t('form.depreciationExpense')}</FormLabel>
                    <Skeleton className="h-10 w-full" />
                  </FormItem>
                ) : (
                  <FormField
                    control={form.control}
                    name="depreciation_expense_account_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.depreciationExpense')}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('form.selectDepreciationExpense')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {expenseAccounts.map((account) => (
                              <SelectItem key={account.id} value={account.id}>
                                {(account.code || account.account_code)} - {(account.name || account.account_name)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Image Upload */}
                <FormField
                  control={form.control}
                  name="image_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.assetImage')}</FormLabel>
                      <FormControl>
                        <FileUpload
                          onUploadComplete={(url) => {
                            console.log('FileUpload component returned URL:', url);
                            field.onChange(url);
                            console.log('Field value after onChange:', form.getValues('image_url'));

                            // Force a form state update to ensure the value is captured
                            form.setValue('image_url', url, {
                              shouldDirty: true,
                              shouldTouch: true,
                              shouldValidate: true
                            });
                          }}
                          currentImageUrl={field.value}
                          maxSizeMB={2}
                          buttonText={t('form.uploadImage')}
                        />
                      </FormControl>
                      <FormDescription>
                        {t('form.imageDescription')}
                        {field.value && (
                          <div className="mt-1 text-xs text-blue-500">
                            {t('form.currentImageUrl')} {field.value.substring(0, 50)}...
                          </div>
                        )}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </div>

        <DialogFooter className="p-6 pt-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
            form="asset-form"
          >
            {t('buttons.cancel')}
          </Button>
          <Button type="submit" disabled={isSubmitting} form="asset-form">
            {isSubmitting ? t('buttons.saving') : asset ? t('buttons.update') : t('buttons.create')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
