'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';

// UI Components
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/safe-dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

// Components
import { EntitySelector } from './EntitySelector';

// Icons
import {
  Link,
  Unlink,
  Warehouse,
  UserCheck,
  Truck,
  Calculator,
  CreditCard,
  Users,
  Package,
  Building,
  AlertTriangle,
  CheckCircle,
  ExternalLink
} from 'lucide-react';

// API Hooks
import {
  useGetBudgetItemIntegrationsQuery,
  useCreateBudgetItemIntegrationMutation,
  useUpdateBudgetItemIntegrationMutation,
  BudgetItemIntegration
} from '@/redux/services/budgetApi';

// Types
interface BudgetItemIntegrationDialogProps {
  budgetItemId: string;
  budgetItemName: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  branchId?: string;
}

interface IntegrationOption {
  type: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  entityIdField: string;
  apiEndpoint: string;
  displayField: string;
}

const integrationOptions: IntegrationOption[] = [
  {
    type: 'inventory',
    name: 'Inventory Item',
    description: 'Link to specific inventory items for stock budgeting',
    icon: <Warehouse className="h-4 w-4" />,
    category: 'Operational',
    entityIdField: 'inventoryItemId',
    apiEndpoint: '/api/inventory-items',
    displayField: 'name'
  },
  {
    type: 'customer',
    name: 'Customer',
    description: 'Link to customers for revenue forecasting',
    icon: <UserCheck className="h-4 w-4" />,
    category: 'Revenue',
    entityIdField: 'customerId',
    apiEndpoint: '/api/customers',
    displayField: 'name'
  },
  {
    type: 'vendor',
    name: 'Vendor',
    description: 'Link to vendors for expense budgeting',
    icon: <Truck className="h-4 w-4" />,
    category: 'Expense',
    entityIdField: 'vendorId',
    apiEndpoint: '/api/vendors',
    displayField: 'name'
  },
  {
    type: 'project',
    name: 'Project/Cost Center',
    description: 'Link to projects for cost center budgeting',
    icon: <Building className="h-4 w-4" />,
    category: 'Project',
    entityIdField: 'projectId',
    apiEndpoint: '/api/projects',
    displayField: 'name'
  },
  {
    type: 'employee',
    name: 'Employee',
    description: 'Link to employees for payroll budgeting',
    icon: <Users className="h-4 w-4" />,
    category: 'HR',
    entityIdField: 'employeeId',
    apiEndpoint: '/api/employees',
    displayField: 'name'
  },
  {
    type: 'asset',
    name: 'Asset',
    description: 'Link to assets for depreciation and maintenance budgeting',
    icon: <Package className="h-4 w-4" />,
    category: 'Asset',
    entityIdField: 'assetId',
    apiEndpoint: '/api/assets',
    displayField: 'name'
  },
  {
    type: 'bank_account',
    name: 'Bank Account',
    description: 'Link to bank accounts for cash flow budgeting',
    icon: <CreditCard className="h-4 w-4" />,
    category: 'Banking',
    entityIdField: 'bankAccountId',
    apiEndpoint: '/api/bank-accounts',
    displayField: 'account_name'
  },
  {
    type: 'tax_category',
    name: 'Tax Category',
    description: 'Link to tax categories for tax planning',
    icon: <Calculator className="h-4 w-4" />,
    category: 'Tax',
    entityIdField: 'taxCategoryId',
    apiEndpoint: '/api/tax-categories',
    displayField: 'name'
  }
];

export function BudgetItemIntegrationDialog({
  budgetItemId,
  budgetItemName,
  open,
  onOpenChange,
  branchId
}: BudgetItemIntegrationDialogProps) {
  const t = useTranslations('budget.integration');
  
  // State
  const [selectedIntegrationType, setSelectedIntegrationType] = useState<string>('');
  const [selectedEntityId, setSelectedEntityId] = useState<string>('');
  const [metadata, setMetadata] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const { data: integrationsData, isLoading, refetch } = useGetBudgetItemIntegrationsQuery(
    { budgetItemId },
    { skip: !budgetItemId || !open }
  );
  
  const [createIntegration, { isLoading: isCreating }] = useCreateBudgetItemIntegrationMutation();
  const [updateIntegration, { isLoading: isUpdating }] = useUpdateBudgetItemIntegrationMutation();

  const existingIntegrations = integrationsData?.integrations || [];

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setSelectedIntegrationType('');
      setSelectedEntityId('');
      setMetadata('');
      setError(null);
    }
  }, [open]);

  const handleCreateIntegration = async () => {
    if (!selectedIntegrationType || !selectedEntityId) {
      setError('Please select both integration type and entity');
      return;
    }

    try {
      setError(null);
      
      const integrationData: Partial<BudgetItemIntegration> = {
        budgetItemId,
        integrationType: selectedIntegrationType,
        metadata: metadata || undefined,
      };

      // Set the appropriate entity ID field
      const option = integrationOptions.find(opt => opt.type === selectedIntegrationType);
      if (option) {
        (integrationData as any)[option.entityIdField] = selectedEntityId;
      }

      await createIntegration(integrationData).unwrap();
      
      // Reset form
      setSelectedIntegrationType('');
      setSelectedEntityId('');
      setMetadata('');
      
      // Refresh integrations
      refetch();
    } catch (err: any) {
      setError(err.data?.message || 'Failed to create integration');
    }
  };

  const handleRemoveIntegration = async (integrationId: string) => {
    try {
      setError(null);
      // Note: You'll need to implement a delete endpoint
      // await deleteIntegration({ id: integrationId }).unwrap();
      refetch();
    } catch (err: any) {
      setError(err.data?.message || 'Failed to remove integration');
    }
  };

  const getIntegrationOption = (type: string) => {
    return integrationOptions.find(opt => opt.type === type);
  };

  const getEntityDisplayName = (integration: BudgetItemIntegration) => {
    // This would need to be enhanced to fetch actual entity names
    // For now, return the entity ID
    const option = getIntegrationOption(integration.integrationType);
    if (!option) return 'Unknown';
    
    const entityId = (integration as any)[option.entityIdField];
    return entityId || 'Unknown';
  };

  const groupedOptions = integrationOptions.reduce((acc, option) => {
    if (!acc[option.category]) {
      acc[option.category] = [];
    }
    acc[option.category].push(option);
    return acc;
  }, {} as Record<string, IntegrationOption[]>);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Link className="h-5 w-5" />
            Budget Item Integrations
          </DialogTitle>
          <DialogDescription>
            Link "{budgetItemName}" to other modules for enhanced budgeting and tracking.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Existing Integrations */}
          {existingIntegrations.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                Active Integrations
              </h3>
              <div className="space-y-2">
                {existingIntegrations.map((integration) => {
                  const option = getIntegrationOption(integration.integrationType);
                  return (
                    <Card key={integration.id} className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {option?.icon}
                          <div>
                            <div className="font-medium">{option?.name}</div>
                            <div className="text-sm text-muted-foreground">
                              Linked to: {getEntityDisplayName(integration)}
                            </div>
                          </div>
                          <Badge variant="secondary">{option?.category}</Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              // Navigate to the linked entity
                              const entityId = (integration as any)[option?.entityIdField];
                              if (entityId && option) {
                                window.open(`${option.apiEndpoint}/${entityId}`, '_blank');
                              }
                            }}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveIntegration(integration.id)}
                          >
                            <Unlink className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </div>
          )}

          <Separator />

          {/* Add New Integration */}
          <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <Link className="h-4 w-4" />
              Add New Integration
            </h3>
            
            <div className="space-y-4">
              {/* Integration Type Selection */}
              <div className="space-y-2">
                <Label>Integration Type</Label>
                <Select value={selectedIntegrationType} onValueChange={setSelectedIntegrationType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select integration type" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(groupedOptions).map(([category, options]) => (
                      <div key={category}>
                        <div className="px-2 py-1 text-sm font-medium text-muted-foreground">
                          {category}
                        </div>
                        {options.map((option) => (
                          <SelectItem key={option.type} value={option.type}>
                            <div className="flex items-center gap-2">
                              {option.icon}
                              <div>
                                <div className="font-medium">{option.name}</div>
                                <div className="text-xs text-muted-foreground">
                                  {option.description}
                                </div>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </div>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Entity Selection */}
              {selectedIntegrationType && (
                <EntitySelector
                  integrationType={selectedIntegrationType}
                  selectedEntityId={selectedEntityId}
                  onEntitySelect={setSelectedEntityId}
                  branchId={branchId}
                />
              )}

              {/* Metadata */}
              <div className="space-y-2">
                <Label>Notes (Optional)</Label>
                <Textarea
                  value={metadata}
                  onChange={(e) => setMetadata(e.target.value)}
                  placeholder="Add any additional notes about this integration..."
                  rows={3}
                />
              </div>

              {/* Error Display */}
              {error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Add Button */}
              <Button
                onClick={handleCreateIntegration}
                disabled={!selectedIntegrationType || !selectedEntityId || isCreating}
                className="w-full"
              >
                {isCreating ? 'Adding Integration...' : 'Add Integration'}
              </Button>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
