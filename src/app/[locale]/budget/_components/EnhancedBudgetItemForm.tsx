'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Icons
import {
  DollarSign,
  Link,
  Calendar,
  FileText,
  Warehouse,
  UserCheck,
  Truck,
  Calculator,
  CreditCard,
  Users,
  Package,
  Building
} from 'lucide-react';

// Types
interface BudgetItemFormData {
  account_id: string;
  year: number;
  month: number;
  amount: number;
  notes: string;
  integrations?: {
    type: string;
    entityId: string;
    metadata?: string;
  }[];
}

interface EnhancedBudgetItemFormProps {
  initialData?: Partial<BudgetItemFormData>;
  accounts: any[];
  onSubmit: (data: BudgetItemFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
  error?: string | null;
}

const integrationTypes = [
  {
    type: 'inventory',
    name: 'Inventory Item',
    icon: <Warehouse className="h-4 w-4" />,
    description: 'Link to inventory for stock budgeting',
    color: 'bg-blue-100 text-blue-800'
  },
  {
    type: 'customer',
    name: 'Customer',
    icon: <UserCheck className="h-4 w-4" />,
    description: 'Link to customer for revenue forecasting',
    color: 'bg-green-100 text-green-800'
  },
  {
    type: 'vendor',
    name: 'Vendor',
    icon: <Truck className="h-4 w-4" />,
    description: 'Link to vendor for expense budgeting',
    color: 'bg-orange-100 text-orange-800'
  },
  {
    type: 'project',
    name: 'Project',
    icon: <Building className="h-4 w-4" />,
    description: 'Link to project for cost center budgeting',
    color: 'bg-purple-100 text-purple-800'
  },
  {
    type: 'employee',
    name: 'Employee',
    icon: <Users className="h-4 w-4" />,
    description: 'Link to employee for payroll budgeting',
    color: 'bg-pink-100 text-pink-800'
  },
  {
    type: 'asset',
    name: 'Asset',
    icon: <Package className="h-4 w-4" />,
    description: 'Link to asset for depreciation budgeting',
    color: 'bg-indigo-100 text-indigo-800'
  },
  {
    type: 'bank_account',
    name: 'Bank Account',
    icon: <CreditCard className="h-4 w-4" />,
    description: 'Link to bank account for cash flow budgeting',
    color: 'bg-cyan-100 text-cyan-800'
  },
  {
    type: 'tax_category',
    name: 'Tax Category',
    icon: <Calculator className="h-4 w-4" />,
    description: 'Link to tax category for tax planning',
    color: 'bg-yellow-100 text-yellow-800'
  }
];

export function EnhancedBudgetItemForm({
  initialData,
  accounts,
  onSubmit,
  onCancel,
  isLoading = false,
  error = null
}: EnhancedBudgetItemFormProps) {
  const t = useTranslations('budget');
  
  const [formData, setFormData] = useState<BudgetItemFormData>({
    account_id: initialData?.account_id || '',
    year: initialData?.year || new Date().getFullYear(),
    month: initialData?.month || new Date().getMonth() + 1,
    amount: initialData?.amount || 0,
    notes: initialData?.notes || '',
    integrations: initialData?.integrations || []
  });

  const [selectedIntegrationType, setSelectedIntegrationType] = useState<string>('');
  const [selectedEntityId, setSelectedEntityId] = useState<string>('');
  const [integrationMetadata, setIntegrationMetadata] = useState<string>('');

  const handleInputChange = (field: keyof BudgetItemFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddIntegration = () => {
    if (!selectedIntegrationType || !selectedEntityId) return;

    const newIntegration = {
      type: selectedIntegrationType,
      entityId: selectedEntityId,
      metadata: integrationMetadata || undefined
    };

    setFormData(prev => ({
      ...prev,
      integrations: [...(prev.integrations || []), newIntegration]
    }));

    // Reset integration form
    setSelectedIntegrationType('');
    setSelectedEntityId('');
    setIntegrationMetadata('');
  };

  const handleRemoveIntegration = (index: number) => {
    setFormData(prev => ({
      ...prev,
      integrations: prev.integrations?.filter((_, i) => i !== index) || []
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const getIntegrationTypeInfo = (type: string) => {
    return integrationTypes.find(t => t.type === type);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Basic Details
          </TabsTrigger>
          <TabsTrigger value="integrations" className="flex items-center gap-2">
            <Link className="h-4 w-4" />
            Integrations
            {formData.integrations && formData.integrations.length > 0 && (
              <Badge variant="secondary" className="ml-1">
                {formData.integrations.length}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Budget Item Details
              </CardTitle>
              <CardDescription>
                Enter the basic information for this budget item.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Account Selection */}
              <div className="space-y-2">
                <Label htmlFor="account">Chart of Account</Label>
                <Select
                  value={formData.account_id}
                  onValueChange={(value) => handleInputChange('account_id', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select account" />
                  </SelectTrigger>
                  <SelectContent>
                    {accounts.map((account) => (
                      <SelectItem key={account.id} value={account.id}>
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-xs">{account.account_code}</span>
                          <span>{account.account_name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Year and Month */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="year">Year</Label>
                  <Select
                    value={formData.year.toString()}
                    onValueChange={(value) => handleInputChange('year', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 5 }, (_, i) => {
                        const year = new Date().getFullYear() + i - 2;
                        return (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="month">Month</Label>
                  <Select
                    value={formData.month.toString()}
                    onValueChange={(value) => handleInputChange('month', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => {
                        const month = i + 1;
                        const monthName = new Date(2000, i, 1).toLocaleString('default', { month: 'long' });
                        return (
                          <SelectItem key={month} value={month.toString()}>
                            {monthName}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Amount */}
              <div className="space-y-2">
                <Label htmlFor="amount">Amount</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  value={formData.amount}
                  onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
                  required
                />
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={3}
                  placeholder="Add any additional notes..."
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="h-5 w-5" />
                Module Integrations
              </CardTitle>
              <CardDescription>
                Link this budget item to other modules for enhanced tracking and forecasting.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Existing Integrations */}
              {formData.integrations && formData.integrations.length > 0 && (
                <div className="space-y-2">
                  <Label>Current Integrations</Label>
                  <div className="space-y-2">
                    {formData.integrations.map((integration, index) => {
                      const typeInfo = getIntegrationTypeInfo(integration.type);
                      return (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            {typeInfo?.icon}
                            <div>
                              <div className="font-medium">{typeInfo?.name}</div>
                              <div className="text-sm text-muted-foreground">
                                Entity ID: {integration.entityId}
                              </div>
                              {integration.metadata && (
                                <div className="text-xs text-muted-foreground">
                                  {integration.metadata}
                                </div>
                              )}
                            </div>
                            <Badge className={typeInfo?.color}>
                              {typeInfo?.name}
                            </Badge>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveIntegration(index)}
                          >
                            Remove
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                  <Separator />
                </div>
              )}

              {/* Add New Integration */}
              <div className="space-y-4">
                <Label>Add Integration</Label>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Integration Type</Label>
                    <Select
                      value={selectedIntegrationType}
                      onValueChange={setSelectedIntegrationType}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        {integrationTypes.map((type) => (
                          <SelectItem key={type.type} value={type.type}>
                            <div className="flex items-center gap-2">
                              {type.icon}
                              <div>
                                <div className="font-medium">{type.name}</div>
                                <div className="text-xs text-muted-foreground">
                                  {type.description}
                                </div>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Entity ID</Label>
                    <Input
                      value={selectedEntityId}
                      onChange={(e) => setSelectedEntityId(e.target.value)}
                      placeholder="Enter entity ID"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Notes (Optional)</Label>
                  <Textarea
                    value={integrationMetadata}
                    onChange={(e) => setIntegrationMetadata(e.target.value)}
                    rows={2}
                    placeholder="Add notes about this integration..."
                  />
                </div>

                <Button
                  type="button"
                  variant="outline"
                  onClick={handleAddIntegration}
                  disabled={!selectedIntegrationType || !selectedEntityId}
                  className="w-full"
                >
                  Add Integration
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Error Display */}
      {error && (
        <div className="text-red-500 text-sm p-3 bg-red-50 border border-red-200 rounded">
          {error}
        </div>
      )}

      {/* Form Actions */}
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save Budget Item'}
        </Button>
      </div>
    </form>
  );
}
