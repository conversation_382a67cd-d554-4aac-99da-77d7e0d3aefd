'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { BudgetIntegrationHelp } from './BudgetIntegrationHelp';
import {
  useGetBudgetIntegrationSettingsQuery,
  useUpdateBudgetIntegrationSettingsMutation,
  BudgetIntegrationSettings
} from '@/redux/services/budgetApi';

// UI Components
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/safe-dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';

// Icons
import {
  Settings,
  DollarSign,
  FileText,
  Users,
  Package,
  Building,
  Bell,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Warehouse,
  UserCheck,
  Truck,
  Calculator,
  Banknote,
  CreditCard,
  PieChart,
  BarChart3,
  Zap,
  Shield
} from 'lucide-react';

// Types
interface BudgetIntegration {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  enabled: boolean;
  category: 'financial' | 'operational' | 'alerts' | 'reporting' | 'advanced' | 'compliance';
  settings?: {
    threshold?: number;
    frequency?: string;
    autoApproval?: boolean;
    notifications?: boolean;
    reorderPoint?: number;
    forecastPeriod?: string;
    reconciliationMode?: string;
    taxCalculation?: string;
    cashFlowPrediction?: boolean;
  };
}

interface BudgetIntegrationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  branchId?: string;
}

export function BudgetIntegrationDialog({
  open,
  onOpenChange,
  branchId
}: BudgetIntegrationDialogProps) {
  const t = useTranslations('budget.integration');
  const [showHelp, setShowHelp] = useState(false);

  // API hooks
  const { data: settingsData, isLoading, error } = useGetBudgetIntegrationSettingsQuery(
    { branchId: branchId || '' },
    { skip: !branchId || !open }
  );
  const [updateSettings, { isLoading: isUpdating }] = useUpdateBudgetIntegrationSettingsMutation();

  // Initialize integrations from API data
  const [integrations, setIntegrations] = useState<BudgetIntegration[]>([]);

  // Update integrations when API data is loaded
  useEffect(() => {
    if (settingsData?.settings) {
      const apiIntegrations = settingsData.settings.map((setting: BudgetIntegrationSettings) => {
        const integrationConfig = getIntegrationConfig(setting.integrationType);
        return {
          id: setting.integrationType,
          name: integrationConfig.name,
          description: integrationConfig.description,
          icon: integrationConfig.icon,
          enabled: setting.enabled,
          category: integrationConfig.category,
          settings: {
            threshold: setting.threshold,
            frequency: setting.frequency,
            autoApproval: setting.autoApproval,
            notifications: setting.notifications,
            reorderPoint: setting.reorderPoint,
            forecastPeriod: setting.forecastPeriod,
            reconciliationMode: setting.reconciliationMode,
            taxCalculation: setting.taxCalculation,
            cashFlowPrediction: setting.cashFlowPrediction,
          }
        };
      });
      setIntegrations(apiIntegrations);
    } else {
      // Fallback to default integrations if no API data
      setIntegrations(getDefaultIntegrations());
    }
  }, [settingsData]);

  // Helper function to get integration configuration
  const getIntegrationConfig = (type: string) => {
    const configs = {
      'chart-of-accounts': {
        name: 'Chart of Accounts',
        description: 'Link budget items to specific account codes for accurate tracking',
        icon: <Building className="h-4 w-4" />,
        category: 'financial' as const
      },
      'inventory': {
        name: 'Inventory Management',
        description: 'Budget for stock investments, reorder points, and inventory valuation',
        icon: <Warehouse className="h-4 w-4" />,
        category: 'advanced' as const
      },
      'customer': {
        name: 'Customer Management',
        description: 'Revenue forecasting by customer segments and sales pipeline budgeting',
        icon: <UserCheck className="h-4 w-4" />,
        category: 'advanced' as const
      },
      'vendor': {
        name: 'Vendor Management',
        description: 'Supplier spending budgets, contract management, and vendor performance tracking',
        icon: <Truck className="h-4 w-4" />,
        category: 'advanced' as const
      },
      'tax': {
        name: 'Tax Management',
        description: 'Tax planning, compliance budgeting, and automated tax provision calculations',
        icon: <Calculator className="h-4 w-4" />,
        category: 'compliance' as const
      },
      'banking': {
        name: 'Banking Integration',
        description: 'Real-time account reconciliation, cash flow forecasting, and bank fee budgeting',
        icon: <CreditCard className="h-4 w-4" />,
        category: 'compliance' as const
      }
    };
    return configs[type as keyof typeof configs] || {
      name: type,
      description: 'Integration module',
      icon: <Settings className="h-4 w-4" />,
      category: 'financial' as const
    };
  };

  // Helper function to get default integrations
  const getDefaultIntegrations = (): BudgetIntegration[] => [
    {
      id: 'invoices',
      name: 'Invoices & Revenue',
      description: 'Track actual revenue against budgeted income forecasts',
      icon: <FileText className="h-4 w-4" />,
      enabled: false,
      category: 'financial',
      settings: { threshold: 90, notifications: true }
    },
    {
      id: 'expenses',
      name: 'Bills & Expenses',
      description: 'Monitor spending against budget limits with real-time tracking',
      icon: <DollarSign className="h-4 w-4" />,
      enabled: false,
      category: 'financial',
      settings: { threshold: 80, notifications: true }
    },
    {
      id: 'purchase-orders',
      name: 'Purchase Orders',
      description: 'Pre-commit budget amounts when creating purchase orders',
      icon: <Package className="h-4 w-4" />,
      enabled: false,
      category: 'operational',
      settings: { autoApproval: false, threshold: 100 }
    },
    
    // Operational Modules
    {
      id: 'projects',
      name: 'Projects & Cost Centers',
      description: 'Allocate and track budgets by specific projects or departments',
      icon: <Target className="h-4 w-4" />,
      enabled: false,
      category: 'operational',
      settings: { threshold: 85, frequency: 'weekly' }
    },
    {
      id: 'payroll',
      name: 'Employee & Payroll',
      description: 'Budget for labor costs and track actual payroll expenses',
      icon: <Users className="h-4 w-4" />,
      enabled: false,
      category: 'operational',
      settings: { threshold: 95, frequency: 'monthly' }
    },
    {
      id: 'assets',
      name: 'Asset Management',
      description: 'Plan capital expenditures and track asset-related costs',
      icon: <Package className="h-4 w-4" />,
      enabled: false,
      category: 'operational',
      settings: { threshold: 100, autoApproval: false }
    },

    // Alerts & Notifications
    {
      id: 'budget-alerts',
      name: 'Budget Alerts',
      description: 'Receive notifications when approaching or exceeding budget limits',
      icon: <Bell className="h-4 w-4" />,
      enabled: false,
      category: 'alerts',
      settings: { threshold: 80, notifications: true }
    },
    {
      id: 'variance-reports',
      name: 'Variance Reporting',
      description: 'Automated budget vs actual reports with variance analysis',
      icon: <TrendingUp className="h-4 w-4" />,
      enabled: false,
      category: 'reporting',
      settings: { frequency: 'monthly', autoApproval: true }
    },

    // Advanced Business Modules
    {
      id: 'inventory-management',
      name: 'Inventory Management',
      description: 'Budget for stock investments, reorder points, and inventory valuation',
      icon: <Warehouse className="h-4 w-4" />,
      enabled: false,
      category: 'advanced',
      settings: {
        threshold: 75,
        frequency: 'weekly',
        notifications: true,
        autoApproval: false,
        reorderPoint: 20 // Percentage of stock level to trigger reorder
      }
    },
    {
      id: 'customer-management',
      name: 'Customer Management',
      description: 'Revenue forecasting by customer segments and sales pipeline budgeting',
      icon: <UserCheck className="h-4 w-4" />,
      enabled: false,
      category: 'advanced',
      settings: {
        threshold: 85,
        frequency: 'monthly',
        notifications: true,
        forecastPeriod: 'quarterly' // quarterly, annual, bi-annual
      }
    },
    {
      id: 'vendor-management',
      name: 'Vendor Management',
      description: 'Supplier spending budgets, contract management, and vendor performance tracking',
      icon: <Truck className="h-4 w-4" />,
      enabled: false,
      category: 'advanced',
      settings: {
        threshold: 90,
        frequency: 'monthly',
        autoApproval: false,
        notifications: true,
        forecastPeriod: 'quarterly'
      }
    },

    // Compliance & Financial Control
    {
      id: 'tax-management',
      name: 'Tax Management',
      description: 'Tax planning, compliance budgeting, and automated tax provision calculations',
      icon: <Calculator className="h-4 w-4" />,
      enabled: false,
      category: 'compliance',
      settings: {
        threshold: 95,
        frequency: 'monthly',
        autoApproval: false,
        notifications: true,
        taxCalculation: 'accrual' // accrual, cash, hybrid
      }
    },
    {
      id: 'banking-integration',
      name: 'Banking Integration',
      description: 'Real-time account reconciliation, cash flow forecasting, and bank fee budgeting',
      icon: <CreditCard className="h-4 w-4" />,
      enabled: false,
      category: 'compliance',
      settings: {
        threshold: 100,
        frequency: 'daily',
        autoApproval: true,
        notifications: true,
        reconciliationMode: 'automatic', // automatic, manual, hybrid
        cashFlowPrediction: true
      }
    }
  ]);

  const toggleIntegration = (id: string) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === id 
        ? { ...integration, enabled: !integration.enabled }
        : integration
    ));
  };

  const updateIntegrationSetting = (id: string, setting: string, value: any) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === id 
        ? { 
            ...integration, 
            settings: { ...integration.settings, [setting]: value }
          }
        : integration
    ));
  };

  const handleSave = async () => {
    if (!branchId) return;

    try {
      // Convert integrations to API format
      const settingsToSave: BudgetIntegrationSettings[] = integrations.map(integration => ({
        id: integration.id,
        branchId,
        integrationType: integration.id as any,
        enabled: integration.enabled,
        threshold: integration.settings?.threshold,
        frequency: integration.settings?.frequency as any,
        autoApproval: integration.settings?.autoApproval,
        notifications: integration.settings?.notifications,
        reorderPoint: integration.settings?.reorderPoint,
        forecastPeriod: integration.settings?.forecastPeriod as any,
        reconciliationMode: integration.settings?.reconciliationMode as any,
        taxCalculation: integration.settings?.taxCalculation as any,
        cashFlowPrediction: integration.settings?.cashFlowPrediction,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }));

      await updateSettings({
        branchId,
        settings: settingsToSave
      }).unwrap();

      console.log('Budget integration settings saved successfully');
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to save integration settings:', error);
    }
  };

  const categorizedIntegrations = {
    financial: integrations.filter(i => i.category === 'financial'),
    operational: integrations.filter(i => i.category === 'operational'),
    alerts: integrations.filter(i => i.category === 'alerts'),
    reporting: integrations.filter(i => i.category === 'reporting'),
    advanced: integrations.filter(i => i.category === 'advanced'),
    compliance: integrations.filter(i => i.category === 'compliance')
  };

  const enabledCount = integrations.filter(i => i.enabled).length;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              {showHelp ? 'Integration Guide' : 'Budget Integration Settings'}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowHelp(!showHelp)}
              className="text-xs"
            >
              {showHelp ? 'Back to Settings' : 'View Guide'}
            </Button>
          </DialogTitle>
          <DialogDescription>
            {showHelp ? (
              'Comprehensive guide to advanced budget integration features'
            ) : (
              <>
                Connect your budget with other modules for comprehensive financial management.
                {enabledCount > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {enabledCount} integration{enabledCount !== 1 ? 's' : ''} enabled
                  </Badge>
                )}
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        {showHelp ? (
          <BudgetIntegrationHelp />
        ) : (
          <div className="space-y-6">
            {/* Financial Modules */}
            <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Financial Modules
            </h3>
            <div className="space-y-4">
              {categorizedIntegrations.financial.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={toggleIntegration}
                  onUpdateSetting={updateIntegrationSetting}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Operational Modules */}
          <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <Target className="h-4 w-4" />
              Operational Modules
            </h3>
            <div className="space-y-4">
              {categorizedIntegrations.operational.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={toggleIntegration}
                  onUpdateSetting={updateIntegrationSetting}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Alerts & Reporting */}
          <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Alerts & Reporting
            </h3>
            <div className="space-y-4">
              {[...categorizedIntegrations.alerts, ...categorizedIntegrations.reporting].map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={toggleIntegration}
                  onUpdateSetting={updateIntegrationSetting}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Advanced Business Modules */}
          <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Advanced Business Modules
            </h3>
            <div className="space-y-4">
              {categorizedIntegrations.advanced.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={toggleIntegration}
                  onUpdateSetting={updateIntegrationSetting}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Compliance & Financial Control */}
          <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Compliance & Financial Control
            </h3>
            <div className="space-y-4">
              {categorizedIntegrations.compliance.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={toggleIntegration}
                  onUpdateSetting={updateIntegrationSetting}
                />
              ))}
            </div>
          </div>

            {/* Warning Alert */}
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Enabling integrations will affect how budget data is processed and may require additional setup in connected modules.
              </AlertDescription>
            </Alert>
          </div>
        )}

        {!showHelp && (
          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isUpdating}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={isUpdating || !branchId}>
              {isUpdating ? 'Saving...' : 'Save Integration Settings'}
            </Button>
          </DialogFooter>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-sm text-muted-foreground">Loading integration settings...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-8">
            <p className="text-sm text-red-600">Failed to load integration settings. Please try again.</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

// Integration Card Component
interface IntegrationCardProps {
  integration: BudgetIntegration;
  onToggle: (id: string) => void;
  onUpdateSetting: (id: string, setting: string, value: any) => void;
}

function IntegrationCard({ integration, onToggle, onUpdateSetting }: IntegrationCardProps) {
  return (
    <div className={`border rounded-lg p-4 ${integration.enabled ? 'bg-muted/50' : ''}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3 flex-1">
          <div className="mt-1">
            {integration.icon}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium">{integration.name}</h4>
              {integration.enabled && (
                <CheckCircle className="h-4 w-4 text-green-600" />
              )}
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              {integration.description}
            </p>
            
            {/* Settings */}
            {integration.enabled && integration.settings && (
              <div className="space-y-3 pt-2 border-t">
                {integration.settings.threshold !== undefined && (
                  <div className="flex items-center gap-2">
                    <Label className="text-xs">Alert Threshold:</Label>
                    <Input
                      type="number"
                      value={integration.settings.threshold}
                      onChange={(e) => onUpdateSetting(integration.id, 'threshold', parseInt(e.target.value))}
                      className="w-20 h-7 text-xs"
                      min="0"
                      max="100"
                    />
                    <span className="text-xs text-muted-foreground">%</span>
                  </div>
                )}
                
                {integration.settings.frequency !== undefined && (
                  <div className="flex items-center gap-2">
                    <Label className="text-xs">Frequency:</Label>
                    <Select
                      value={integration.settings.frequency}
                      onValueChange={(value) => onUpdateSetting(integration.id, 'frequency', value)}
                    >
                      <SelectTrigger className="w-24 h-7 text-xs">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                
                {integration.settings.autoApproval !== undefined && (
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={integration.settings.autoApproval}
                      onCheckedChange={(checked) => onUpdateSetting(integration.id, 'autoApproval', checked)}
                    />
                    <Label className="text-xs">Auto-approval</Label>
                  </div>
                )}
                
                {integration.settings.notifications !== undefined && (
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={integration.settings.notifications}
                      onCheckedChange={(checked) => onUpdateSetting(integration.id, 'notifications', checked)}
                    />
                    <Label className="text-xs">Email notifications</Label>
                  </div>
                )}

                {/* Advanced Settings for Inventory Management */}
                {integration.settings.reorderPoint !== undefined && (
                  <div className="flex items-center gap-2">
                    <Label className="text-xs">Reorder Point:</Label>
                    <Input
                      type="number"
                      value={integration.settings.reorderPoint}
                      onChange={(e) => onUpdateSetting(integration.id, 'reorderPoint', parseInt(e.target.value))}
                      className="w-20 h-7 text-xs"
                      min="0"
                      max="100"
                    />
                    <span className="text-xs text-muted-foreground">% stock level</span>
                  </div>
                )}

                {/* Advanced Settings for Customer/Vendor Management */}
                {integration.settings.forecastPeriod !== undefined && (
                  <div className="flex items-center gap-2">
                    <Label className="text-xs">Forecast Period:</Label>
                    <Select
                      value={integration.settings.forecastPeriod}
                      onValueChange={(value) => onUpdateSetting(integration.id, 'forecastPeriod', value)}
                    >
                      <SelectTrigger className="w-28 h-7 text-xs">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="annual">Annual</SelectItem>
                        <SelectItem value="bi-annual">Bi-Annual</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Advanced Settings for Tax Management */}
                {integration.settings.taxCalculation !== undefined && (
                  <div className="flex items-center gap-2">
                    <Label className="text-xs">Tax Method:</Label>
                    <Select
                      value={integration.settings.taxCalculation}
                      onValueChange={(value) => onUpdateSetting(integration.id, 'taxCalculation', value)}
                    >
                      <SelectTrigger className="w-24 h-7 text-xs">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="accrual">Accrual</SelectItem>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="hybrid">Hybrid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Advanced Settings for Banking Integration */}
                {integration.settings.reconciliationMode !== undefined && (
                  <div className="flex items-center gap-2">
                    <Label className="text-xs">Reconciliation:</Label>
                    <Select
                      value={integration.settings.reconciliationMode}
                      onValueChange={(value) => onUpdateSetting(integration.id, 'reconciliationMode', value)}
                    >
                      <SelectTrigger className="w-24 h-7 text-xs">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="automatic">Automatic</SelectItem>
                        <SelectItem value="manual">Manual</SelectItem>
                        <SelectItem value="hybrid">Hybrid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {integration.settings.cashFlowPrediction !== undefined && (
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={integration.settings.cashFlowPrediction}
                      onCheckedChange={(checked) => onUpdateSetting(integration.id, 'cashFlowPrediction', checked)}
                    />
                    <Label className="text-xs">Cash flow prediction</Label>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        
        <Switch
          checked={integration.enabled}
          onCheckedChange={() => onToggle(integration.id)}
        />
      </div>
    </div>
  );
}
