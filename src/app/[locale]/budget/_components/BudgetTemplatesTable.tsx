'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Copy, 
  Play,
  Download,
  Eye
} from 'lucide-react';

// UI Components
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { PaginatedTable, type TableColumn } from '@/components/common';

// Types
import { BudgetTemplate } from '@/lib/types';

// Hooks
import { useAdvancedPagination } from '@/hooks';
import { useModalState } from '@/hooks/useModalState';
import {
  useGetBudgetTemplatesQuery,
  useDeleteBudgetTemplateMutation,
  useApplyBudgetTemplateMutation,
  useDuplicateBudgetTemplateMutation
} from '@/redux/services/budgetApi';

// Components
import { BudgetTemplateDialog } from './BudgetTemplateDialog';
import { ApplyTemplateDialog } from './ApplyTemplateDialog';
import { DeleteConfirmDialog } from '@/components/common/DeleteConfirmDialog';

interface BudgetTemplatesTableProps {
  branchId?: string;
  organizationId?: string;
  year: number;
  month: number;
}

export function BudgetTemplatesTable({ 
  branchId, 
  organizationId, 
  year, 
  month 
}: BudgetTemplatesTableProps) {
  const t = useTranslations('budget.templates');
  
  // State for dialogs
  const [editingTemplate, setEditingTemplate] = useState<BudgetTemplate | null>(null);
  const [deletingTemplate, setDeletingTemplate] = useState<BudgetTemplate | null>(null);
  const [applyingTemplate, setApplyingTemplate] = useState<BudgetTemplate | null>(null);

  // Advanced pagination hook
  const {
    state,
    actions
  } = useAdvancedPagination({
    initialLimit: 10,
    initialSort: { sortBy: 'createdAt', sortOrder: 'desc' }
  });

  // API queries and mutations
  const {
    data: allTemplates,
    isLoading,
    error,
    isFetching
  } = useGetBudgetTemplatesQuery({ branchId, organizationId });

  const [deleteTemplate, { isLoading: isDeleting }] = useDeleteBudgetTemplateMutation();
  const [applyTemplate, { isLoading: isApplying }] = useApplyBudgetTemplateMutation();
  const [duplicateTemplate, { isLoading: isDuplicating }] = useDuplicateBudgetTemplateMutation();

  // Debug logging
  console.log('BudgetTemplatesTable - API Response:', { allTemplates, isLoading, error });
  console.log('BudgetTemplatesTable - allTemplates type:', typeof allTemplates);
  console.log('BudgetTemplatesTable - allTemplates isArray:', Array.isArray(allTemplates));

  // Cleanup effect to ensure no modal-related DOM issues
  useEffect(() => {
    return () => {
      // Cleanup any potential modal artifacts
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';

      // Remove any potential aria-hidden attributes
      const elements = document.querySelectorAll('[aria-hidden="true"]');
      elements.forEach(el => {
        if (el !== document.body) {
          el.removeAttribute('aria-hidden');
        }
      });
    };
  }, []);

  // Effect to handle modal state changes
  useEffect(() => {
    const hasOpenModal = !!(editingTemplate || applyingTemplate || deletingTemplate);

    if (!hasOpenModal) {
      // Ensure page is interactive when no modals are open
      setTimeout(() => {
        document.body.style.overflow = '';
        document.documentElement.style.overflow = '';
        document.body.style.pointerEvents = '';

        // Remove any lingering modal overlays
        const overlays = document.querySelectorAll('[data-radix-popper-content-wrapper]');
        overlays.forEach(overlay => {
          if (overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
          }
        });
      }, 150);
    }
  }, [editingTemplate, applyingTemplate, deletingTemplate]);

  // Client-side filtering and pagination
  const filteredTemplates = useMemo(() => {
    // Handle different response formats
    let templatesArray: BudgetTemplate[] = [];

    if (Array.isArray(allTemplates)) {
      templatesArray = allTemplates;
    } else if (allTemplates && typeof allTemplates === 'object') {
      // Check if it's wrapped in a data property or similar
      if ('data' in allTemplates && Array.isArray(allTemplates.data)) {
        templatesArray = allTemplates.data;
      } else if ('templates' in allTemplates && Array.isArray(allTemplates.templates)) {
        templatesArray = allTemplates.templates;
      } else {
        console.warn('Unexpected templates response format:', allTemplates);
        return [];
      }
    } else {
      return [];
    }

    let filtered = [...templatesArray];

    // Apply search filter
    if (state.search) {
      const searchLower = state.search.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchLower) ||
        template.description?.toLowerCase().includes(searchLower)
      );
    }

    // Apply sorting
    if (state.sortBy) {
      filtered.sort((a, b) => {
        let aValue = a[state.sortBy as keyof typeof a];
        let bValue = b[state.sortBy as keyof typeof b];

        // Handle special cases
        if (state.sortBy === 'itemCount') {
          aValue = a.items?.length || 0;
          bValue = b.items?.length || 0;
        }

        if (aValue === undefined || aValue === null) aValue = '';
        if (bValue === undefined || bValue === null) bValue = '';

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          const comparison = aValue.localeCompare(bValue);
          return state.sortOrder === 'desc' ? -comparison : comparison;
        }

        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return state.sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
        }

        if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
          return state.sortOrder === 'desc' ?
            (bValue ? 1 : 0) - (aValue ? 1 : 0) :
            (aValue ? 1 : 0) - (bValue ? 1 : 0);
        }

        return 0;
      });
    }

    return filtered;
  }, [allTemplates, state.search, state.sortBy, state.sortOrder]);

  // Client-side pagination
  const paginatedTemplates = useMemo(() => {
    const startIndex = (state.page - 1) * state.limit;
    const endIndex = startIndex + state.limit;
    return filteredTemplates.slice(startIndex, endIndex);
  }, [filteredTemplates, state.page, state.limit]);

  // Calculate pagination info
  const paginationInfo = useMemo(() => {
    const total = filteredTemplates.length;
    const totalPages = Math.ceil(total / state.limit);
    return {
      total,
      page: state.page,
      limit: state.limit,
      totalPages,
      hasNextPage: state.page < totalPages,
      hasPreviousPage: state.page > 1,
    };
  }, [filteredTemplates.length, state.page, state.limit]);

  // Handle actions
  const handleEdit = (template: BudgetTemplate) => {
    setEditingTemplate(template);
  };

  const handleDelete = async () => {
    if (!deletingTemplate) return;
    
    try {
      await deleteTemplate(deletingTemplate.id).unwrap();
      setDeletingTemplate(null);
    } catch (error) {
      console.error('Failed to delete template:', error);
    }
  };

  const handleApply = async (selectedYear: number) => {
    if (!applyingTemplate) return;
    
    try {
      await applyTemplate({
        id: applyingTemplate.id,
        year: selectedYear,
        month: month // Use current month from props
      }).unwrap();
      setApplyingTemplate(null);
    } catch (error) {
      console.error('Failed to apply template:', error);
    }
  };

  const handleDuplicate = (template: BudgetTemplate) => {
    // Create a new template based on the existing one
    setEditingTemplate({
      ...template,
      id: '', // Clear ID to create new
      name: `${template.name} (Copy)`,
      isDefault: false
    });
  };

  // Table columns definition
  const columns: TableColumn<BudgetTemplate>[] = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
      render: (value, template) => (
        <div className="font-medium">
          {template?.name || 'Untitled'}
          {template?.isDefault && (
            <Badge variant="secondary" className="ml-2 text-xs">
              Default
            </Badge>
          )}
        </div>
      )
    },
    {
      key: 'description',
      header: 'Description',
      render: (value, template) => (
        <div className="text-sm text-muted-foreground max-w-xs truncate">
          {template?.description || 'No description'}
        </div>
      )
    },
    {
      key: 'itemCount',
      header: 'Items',
      sortable: true,
      render: (value, template) => (
        <div className="text-center">
          {template?.itemCount || template?.items?.length || 0}
        </div>
      )
    },

    {
      key: 'createdAt',
      header: 'Created',
      sortable: true,
      render: (value, template) => (
        <div className="text-sm">
          {template?.createdAt ? new Date(template.createdAt).toLocaleDateString() : 'N/A'}
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (value, template) => {
        if (!template) return null;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEdit(template)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDuplicate(template)}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setApplyingTemplate(template)}>
                <Play className="mr-2 h-4 w-4" />
                Apply
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                Export
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => setDeletingTemplate(template)}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      }
    }
  ];

  return (
    <>
      <PaginatedTable
        data={paginatedTemplates}
        columns={columns}
        paginationInfo={paginationInfo}
        isLoading={isLoading}
        error={error ? String(error) : null}
        isFetching={isFetching}

        // Search functionality
        searchable
        searchPlaceholder="Search templates..."
        searchValue={state.search}
        onSearchChange={actions.handleSearch}

        // Sorting
        sortBy={state.sortBy}
        sortOrder={state.sortOrder}
        onSortChange={actions.handleSort}

        // Pagination
        currentPage={state.page}
        pageSize={state.limit}
        onPageChange={actions.setPage}
        onPageSizeChange={actions.setLimit}
        
        // Display options
        title="Budget Templates"
        description="Manage your reusable budget templates"
        emptyMessage={
          state.search
            ? "No templates match your search criteria"
            : "No templates found"
        }
        
        // Pagination controls
        showPagination={true}
        showPageSizeSelector={true}
      />

      {/* Edit/Create Template Dialog */}
      <BudgetTemplateDialog
        key={editingTemplate?.id || 'new'}
        template={editingTemplate}
        open={!!editingTemplate}
        onOpenChange={(open: boolean) => {
          if (!open) {
            setEditingTemplate(null);
          }
        }}
        branchId={branchId}
      />

      {/* Apply Template Dialog */}
      <ApplyTemplateDialog
        key={applyingTemplate?.id || 'apply'}
        template={applyingTemplate}
        open={!!applyingTemplate}
        onOpenChange={(open: boolean) => {
          if (!open) {
            setApplyingTemplate(null);
          }
        }}
        onApply={handleApply}
        isLoading={isApplying}
        defaultYear={year}
        defaultMonth={month}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        key={deletingTemplate?.id || 'delete'}
        open={!!deletingTemplate}
        onOpenChange={(open: boolean) => {
          if (!open) {
            setDeletingTemplate(null);
          }
        }}
        onConfirm={handleDelete}
        isLoading={isDeleting}
        title="Delete Template"
        description={`Are you sure you want to delete the template "${deletingTemplate?.name}"? This action cannot be undone.`}
      />
    </>
  );
}
