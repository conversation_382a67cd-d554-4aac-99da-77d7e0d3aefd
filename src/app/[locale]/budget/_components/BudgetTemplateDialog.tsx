'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// UI Components
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/safe-dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Types
import { BudgetTemplate } from '@/lib/types';

// Hooks
import { 
  useCreateBudgetTemplateMutation,
  useUpdateBudgetTemplateMutation
} from '@/redux/services/budgetApi';

// Form validation schema
const templateFormSchema = z.object({
  name: z.string().min(1, 'Template name is required').max(255, 'Name too long'),
  description: z.string().optional(),
  isDefault: z.boolean().default(false),
});

type TemplateFormData = z.infer<typeof templateFormSchema>;

interface BudgetTemplateDialogProps {
  template?: BudgetTemplate | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  branchId?: string;
}

export function BudgetTemplateDialog({
  template,
  open,
  onOpenChange,
  branchId
}: BudgetTemplateDialogProps) {
  const t = useTranslations('budget.templates');
  const [error, setError] = useState<string | null>(null);

  // API mutations
  const [createTemplate, { isLoading: isCreating }] = useCreateBudgetTemplateMutation();
  const [updateTemplate, { isLoading: isUpdating }] = useUpdateBudgetTemplateMutation();

  const isEditing = !!template?.id;
  const isLoading = isCreating || isUpdating;

  // Form setup
  const form = useForm<TemplateFormData>({
    resolver: zodResolver(templateFormSchema),
    defaultValues: {
      name: '',
      description: '',
      isDefault: false,
    },
  });

  // Reset form when template changes
  useEffect(() => {
    if (template) {
      form.reset({
        name: template.name,
        description: template.description || '',
        isDefault: template.isDefault || false,
      });
    } else {
      form.reset({
        name: '',
        description: '',
        isDefault: false,
      });
    }
    setError(null);
  }, [template, form]);

  // Handle form submission
  const onSubmit = async (data: TemplateFormData) => {
    if (!branchId && !isEditing) {
      setError('Branch ID is required to create a template');
      return;
    }

    setError(null);

    try {
      if (isEditing && template?.id) {
        // Update existing template
        await updateTemplate({
          id: template.id,
          body: {
            name: data.name,
            description: data.description || undefined,
            isDefault: data.isDefault,
          }
        }).unwrap();
      } else {
        // Create new template
        await createTemplate({
          name: data.name,
          description: data.description || undefined,
          isDefault: data.isDefault,
          items: [], // Start with empty items - can be added later
          branchId: branchId!,
        }).unwrap();
      }

      // Close dialog on success
      onOpenChange(false);
    } catch (err: any) {
      console.error('Failed to save template:', err);
      setError(
        err?.data?.error || 
        err?.message || 
        'Failed to save template. Please try again.'
      );
    }
  };

  const handleClose = () => {
    form.reset();
    setError(null);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Template' : 'Create Template'}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update the template details below.'
              : 'Create a new budget template that can be reused.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Template Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Template Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter template name"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    A unique name for this budget template.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Template Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter template description (optional)"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional description for this template.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Template Options */}
            <div className="space-y-3">
              <FormField
                control={form.control}
                name="isDefault"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        Set as Default Template
                      </FormLabel>
                      <FormDescription>
                        This template will be used as the default when creating new budgets.
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />


            </div>

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleClose}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading
                  ? (isEditing ? 'Updating...' : 'Creating...')
                  : (isEditing ? 'Update Template' : 'Create Template')
                }
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
