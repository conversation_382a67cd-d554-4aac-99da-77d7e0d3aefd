'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Icons
import {
  Link,
  Warehouse,
  UserCheck,
  Truck,
  Calculator,
  CreditCard,
  Users,
  Package,
  Building,
  ExternalLink,
  Plus
} from 'lucide-react';

// API Hooks
import { useGetBudgetItemIntegrationsQuery } from '@/redux/services/budgetApi';

// Types
interface BudgetItemIntegrationBadgesProps {
  budgetItemId: string;
  onManageIntegrations: () => void;
  compact?: boolean;
}

const integrationIcons: Record<string, React.ReactNode> = {
  inventory: <Warehouse className="h-3 w-3" />,
  customer: <UserCheck className="h-3 w-3" />,
  vendor: <Truck className="h-3 w-3" />,
  project: <Building className="h-3 w-3" />,
  employee: <Users className="h-3 w-3" />,
  asset: <Package className="h-3 w-3" />,
  bank_account: <CreditCard className="h-3 w-3" />,
  tax_category: <Calculator className="h-3 w-3" />,
};

const integrationLabels: Record<string, string> = {
  inventory: 'Inventory',
  customer: 'Customer',
  vendor: 'Vendor',
  project: 'Project',
  employee: 'Employee',
  asset: 'Asset',
  bank_account: 'Bank Account',
  tax_category: 'Tax Category',
};

const integrationColors: Record<string, string> = {
  inventory: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
  customer: 'bg-green-100 text-green-800 hover:bg-green-200',
  vendor: 'bg-orange-100 text-orange-800 hover:bg-orange-200',
  project: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
  employee: 'bg-pink-100 text-pink-800 hover:bg-pink-200',
  asset: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',
  bank_account: 'bg-cyan-100 text-cyan-800 hover:bg-cyan-200',
  tax_category: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
};

export function BudgetItemIntegrationBadges({
  budgetItemId,
  onManageIntegrations,
  compact = false
}: BudgetItemIntegrationBadgesProps) {
  const { data: integrationsData, isLoading } = useGetBudgetItemIntegrationsQuery(
    { budgetItemId },
    { skip: !budgetItemId }
  );

  const integrations = integrationsData?.integrations || [];

  if (isLoading) {
    return (
      <div className="flex items-center gap-1">
        <div className="h-5 w-16 bg-gray-200 rounded animate-pulse" />
      </div>
    );
  }

  const handleNavigateToEntity = (integration: any) => {
    // Navigate to the linked entity based on integration type
    const entityRoutes: Record<string, string> = {
      inventory: '/inventory',
      customer: '/customers',
      vendor: '/vendors',
      project: '/projects',
      employee: '/employees',
      asset: '/assets',
      bank_account: '/banking/accounts',
      tax_category: '/tax/categories',
    };

    const route = entityRoutes[integration.integrationType];
    if (route) {
      // Get the entity ID from the integration
      const entityIdFields: Record<string, string> = {
        inventory: 'inventoryItemId',
        customer: 'customerId',
        vendor: 'vendorId',
        project: 'projectId',
        employee: 'employeeId',
        asset: 'assetId',
        bank_account: 'bankAccountId',
        tax_category: 'taxCategoryId',
      };

      const entityIdField = entityIdFields[integration.integrationType];
      const entityId = integration[entityIdField];
      
      if (entityId) {
        // Open in new tab
        window.open(`${route}/${entityId}`, '_blank');
      } else {
        // Navigate to the module's main page
        window.open(route, '_blank');
      }
    }
  };

  if (compact) {
    return (
      <div className="flex items-center gap-1">
        {integrations.length > 0 ? (
          <>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge 
                    variant="secondary" 
                    className="cursor-pointer hover:bg-gray-200"
                    onClick={onManageIntegrations}
                  >
                    <Link className="h-3 w-3 mr-1" />
                    {integrations.length}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1">
                    <div className="font-medium">Linked to:</div>
                    {integrations.map((integration, index) => (
                      <div key={index} className="flex items-center gap-1 text-xs">
                        {integrationIcons[integration.integrationType]}
                        {integrationLabels[integration.integrationType]}
                      </div>
                    ))}
                    <div className="text-xs text-muted-foreground mt-1">
                      Click to manage integrations
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </>
        ) : (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                  onClick={onManageIntegrations}
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-xs">Add integrations</div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center gap-1 flex-wrap">
      {integrations.map((integration, index) => (
        <TooltipProvider key={index}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge
                variant="secondary"
                className={`cursor-pointer transition-colors ${
                  integrationColors[integration.integrationType] || 'hover:bg-gray-200'
                }`}
                onClick={() => handleNavigateToEntity(integration)}
              >
                <div className="flex items-center gap-1">
                  {integrationIcons[integration.integrationType]}
                  <span className="text-xs">
                    {integrationLabels[integration.integrationType]}
                  </span>
                  <ExternalLink className="h-2 w-2 ml-1" />
                </div>
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1">
                <div className="font-medium">
                  {integrationLabels[integration.integrationType]} Integration
                </div>
                <div className="text-xs text-muted-foreground">
                  Click to view linked {integrationLabels[integration.integrationType].toLowerCase()}
                </div>
                {integration.metadata && (
                  <div className="text-xs">
                    <strong>Notes:</strong> {integration.metadata}
                  </div>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ))}
      
      {/* Add Integration Button */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
              onClick={onManageIntegrations}
            >
              <Plus className="h-3 w-3" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-xs">
              {integrations.length > 0 ? 'Manage integrations' : 'Add integrations'}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
