'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Warehouse, 
  UserCheck, 
  Truck, 
  Calculator, 
  CreditCard,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';

export function BudgetIntegrationHelp() {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Advanced Budget Integration Guide</h2>
        <p className="text-muted-foreground">
          Connect your budget with powerful business modules for comprehensive financial management
        </p>
      </div>

      <Separator />

      {/* Inventory Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Warehouse className="h-5 w-5" />
            Inventory Management Integration
            <Badge variant="secondary">Advanced</Badge>
          </CardTitle>
          <CardDescription>
            Budget for stock investments, reorder points, and inventory valuation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                Key Features
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Automatic stock investment budgeting</li>
                <li>• Reorder point threshold management</li>
                <li>• Inventory valuation tracking</li>
                <li>• Seasonal demand forecasting</li>
                <li>• Dead stock identification</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Info className="h-4 w-4 text-blue-600" />
                Configuration
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• <strong>Reorder Point:</strong> % of stock level to trigger alerts</li>
                <li>• <strong>Budget Threshold:</strong> Alert when approaching limit</li>
                <li>• <strong>Frequency:</strong> Weekly monitoring recommended</li>
                <li>• <strong>Notifications:</strong> Email alerts for low stock</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customer Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Customer Management Integration
            <Badge variant="secondary">Advanced</Badge>
          </CardTitle>
          <CardDescription>
            Revenue forecasting by customer segments and sales pipeline budgeting
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                Key Features
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Customer segment revenue forecasting</li>
                <li>• Sales pipeline budget allocation</li>
                <li>• Customer lifetime value tracking</li>
                <li>• Seasonal revenue pattern analysis</li>
                <li>• Customer acquisition cost budgeting</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Info className="h-4 w-4 text-blue-600" />
                Configuration
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• <strong>Forecast Period:</strong> Quarterly/Annual planning</li>
                <li>• <strong>Budget Threshold:</strong> Revenue target monitoring</li>
                <li>• <strong>Frequency:</strong> Monthly review cycles</li>
                <li>• <strong>Notifications:</strong> Pipeline milestone alerts</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vendor Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Vendor Management Integration
            <Badge variant="secondary">Advanced</Badge>
          </CardTitle>
          <CardDescription>
            Supplier spending budgets, contract management, and vendor performance tracking
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                Key Features
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Vendor-specific spending budgets</li>
                <li>• Contract renewal budget planning</li>
                <li>• Vendor performance cost analysis</li>
                <li>• Bulk purchase optimization</li>
                <li>• Payment terms impact tracking</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Info className="h-4 w-4 text-blue-600" />
                Configuration
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• <strong>Forecast Period:</strong> Quarterly contract planning</li>
                <li>• <strong>Budget Threshold:</strong> Vendor spending limits</li>
                <li>• <strong>Auto-approval:</strong> Disabled for cost control</li>
                <li>• <strong>Notifications:</strong> Contract renewal alerts</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tax Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Tax Management Integration
            <Badge variant="outline">Compliance</Badge>
          </CardTitle>
          <CardDescription>
            Tax planning, compliance budgeting, and automated tax provision calculations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                Key Features
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Automated tax provision calculations</li>
                <li>• Quarterly tax payment budgeting</li>
                <li>• Tax compliance cost tracking</li>
                <li>• Multi-jurisdiction tax planning</li>
                <li>• Tax optimization strategies</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Info className="h-4 w-4 text-blue-600" />
                Configuration
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• <strong>Tax Method:</strong> Accrual/Cash/Hybrid basis</li>
                <li>• <strong>Budget Threshold:</strong> 95% for compliance safety</li>
                <li>• <strong>Auto-approval:</strong> Disabled for accuracy</li>
                <li>• <strong>Notifications:</strong> Filing deadline alerts</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Banking Integration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Banking Integration
            <Badge variant="outline">Compliance</Badge>
          </CardTitle>
          <CardDescription>
            Real-time account reconciliation, cash flow forecasting, and bank fee budgeting
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                Key Features
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Real-time account reconciliation</li>
                <li>• Cash flow forecasting</li>
                <li>• Bank fee and charge budgeting</li>
                <li>• Multi-currency support</li>
                <li>• Automated transaction categorization</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Info className="h-4 w-4 text-blue-600" />
                Configuration
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• <strong>Reconciliation:</strong> Automatic/Manual/Hybrid</li>
                <li>• <strong>Cash Flow Prediction:</strong> AI-powered forecasting</li>
                <li>• <strong>Frequency:</strong> Daily monitoring</li>
                <li>• <strong>Auto-approval:</strong> Enabled for efficiency</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Implementation Tips */}
      <Card className="border-amber-200 bg-amber-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-amber-800">
            <AlertCircle className="h-5 w-5" />
            Implementation Best Practices
          </CardTitle>
        </CardHeader>
        <CardContent className="text-amber-700">
          <ul className="space-y-2 text-sm">
            <li>• <strong>Start Small:</strong> Enable one integration at a time to ensure proper setup</li>
            <li>• <strong>Test Thoroughly:</strong> Use test data before enabling in production</li>
            <li>• <strong>Monitor Closely:</strong> Review integration performance weekly for the first month</li>
            <li>• <strong>Train Users:</strong> Ensure team understands new workflows and alerts</li>
            <li>• <strong>Regular Reviews:</strong> Adjust thresholds and settings based on business changes</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
