'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Plus, Download, Upload, Settings } from 'lucide-react';

// UI Components
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Components
import { BudgetTemplateDialog } from './BudgetTemplateDialog';

interface BudgetTemplatesHeaderProps {
  branchId?: string;
  onImport?: () => void;
  onExportAll?: () => void;
  onSettings?: () => void;
}

export function BudgetTemplatesHeader({
  branchId,
  onImport,
  onExportAll,
  onSettings
}: BudgetTemplatesHeaderProps) {
  const t = useTranslations('budget.templates');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  return (
    <div className="flex justify-between items-center">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">{t('title')}</h2>
        <p className="text-muted-foreground">
          {t('description')}
        </p>
      </div>

      <div className="flex items-center gap-2">
        {/* Actions Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              {t('actions')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t('bulkActions')}</DropdownMenuLabel>
            
            {onExportAll && (
              <DropdownMenuItem onClick={onExportAll}>
                <Download className="mr-2 h-4 w-4" />
                {t('exportAll')}
              </DropdownMenuItem>
            )}
            
            {onImport && (
              <DropdownMenuItem onClick={onImport}>
                <Upload className="mr-2 h-4 w-4" />
                {t('import')}
              </DropdownMenuItem>
            )}
            
            <DropdownMenuSeparator />
            
            {onSettings && (
              <DropdownMenuItem onClick={onSettings}>
                <Settings className="mr-2 h-4 w-4" />
                {t('settings')}
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Create Template Button */}
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          {t('createTemplate')}
        </Button>
      </div>

      {/* Create Template Dialog */}
      <BudgetTemplateDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        branchId={branchId}
      />
    </div>
  );
}
