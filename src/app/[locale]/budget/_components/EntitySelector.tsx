'use client';

import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

// Icons
import {
  Search,
  ExternalLink,
  Warehouse,
  UserCheck,
  Truck,
  Calculator,
  CreditCard,
  Users,
  Package,
  Building
} from 'lucide-react';

// Types
interface Entity {
  id: string;
  name: string;
  code?: string;
  description?: string;
  status?: string;
}

interface EntitySelectorProps {
  integrationType: string;
  selectedEntityId: string;
  onEntitySelect: (entityId: string) => void;
  branchId?: string;
}

const integrationConfig: Record<string, {
  name: string;
  icon: React.ReactNode;
  apiEndpoint: string;
  displayField: string;
  searchField: string;
  mockData: Entity[];
}> = {
  inventory: {
    name: 'Inventory Item',
    icon: <Warehouse className="h-4 w-4" />,
    apiEndpoint: '/api/inventory-items',
    displayField: 'name',
    searchField: 'name',
    mockData: [
      { id: 'inv-001', name: 'Office Supplies', code: 'OFF-001', description: 'General office supplies' },
      { id: 'inv-002', name: 'Computer Equipment', code: 'COMP-001', description: 'Laptops, monitors, keyboards' },
      { id: 'inv-003', name: 'Furniture', code: 'FURN-001', description: 'Desks, chairs, cabinets' },
      { id: 'inv-004', name: 'Software Licenses', code: 'SOFT-001', description: 'Annual software subscriptions' },
    ]
  },
  customer: {
    name: 'Customer',
    icon: <UserCheck className="h-4 w-4" />,
    apiEndpoint: '/api/customers',
    displayField: 'name',
    searchField: 'name',
    mockData: [
      { id: 'cust-001', name: 'Acme Corporation', code: 'ACME', description: 'Large enterprise client' },
      { id: 'cust-002', name: 'TechStart Inc.', code: 'TECH', description: 'Growing startup company' },
      { id: 'cust-003', name: 'Global Solutions Ltd.', code: 'GLOB', description: 'International consulting firm' },
      { id: 'cust-004', name: 'Local Business Co.', code: 'LOCAL', description: 'Regional service provider' },
    ]
  },
  vendor: {
    name: 'Vendor',
    icon: <Truck className="h-4 w-4" />,
    apiEndpoint: '/api/vendors',
    displayField: 'name',
    searchField: 'name',
    mockData: [
      { id: 'vend-001', name: 'Office Depot', code: 'DEPOT', description: 'Office supplies vendor' },
      { id: 'vend-002', name: 'Dell Technologies', code: 'DELL', description: 'Computer hardware supplier' },
      { id: 'vend-003', name: 'Microsoft Corporation', code: 'MSFT', description: 'Software licensing' },
      { id: 'vend-004', name: 'Amazon Web Services', code: 'AWS', description: 'Cloud infrastructure provider' },
    ]
  },
  project: {
    name: 'Project',
    icon: <Building className="h-4 w-4" />,
    apiEndpoint: '/api/projects',
    displayField: 'name',
    searchField: 'name',
    mockData: [
      { id: 'proj-001', name: 'Website Redesign', code: 'WEB-2024', description: 'Company website overhaul' },
      { id: 'proj-002', name: 'Mobile App Development', code: 'APP-2024', description: 'New mobile application' },
      { id: 'proj-003', name: 'Office Renovation', code: 'RENO-2024', description: 'Headquarters renovation project' },
      { id: 'proj-004', name: 'Marketing Campaign', code: 'MARK-2024', description: 'Q2 marketing initiative' },
    ]
  },
  employee: {
    name: 'Employee',
    icon: <Users className="h-4 w-4" />,
    apiEndpoint: '/api/employees',
    displayField: 'name',
    searchField: 'name',
    mockData: [
      { id: 'emp-001', name: 'John Smith', code: 'JS001', description: 'Senior Developer' },
      { id: 'emp-002', name: 'Sarah Johnson', code: 'SJ002', description: 'Project Manager' },
      { id: 'emp-003', name: 'Mike Davis', code: 'MD003', description: 'Marketing Director' },
      { id: 'emp-004', name: 'Lisa Chen', code: 'LC004', description: 'Financial Analyst' },
    ]
  },
  asset: {
    name: 'Asset',
    icon: <Package className="h-4 w-4" />,
    apiEndpoint: '/api/assets',
    displayField: 'name',
    searchField: 'name',
    mockData: [
      { id: 'asset-001', name: 'Company Vehicle #1', code: 'VEH-001', description: 'Toyota Camry 2023' },
      { id: 'asset-002', name: 'Server Equipment', code: 'SRV-001', description: 'Dell PowerEdge R740' },
      { id: 'asset-003', name: 'Office Building', code: 'BLDG-001', description: 'Main headquarters building' },
      { id: 'asset-004', name: 'Manufacturing Equipment', code: 'MFG-001', description: 'Production line machinery' },
    ]
  },
  bank_account: {
    name: 'Bank Account',
    icon: <CreditCard className="h-4 w-4" />,
    apiEndpoint: '/api/bank-accounts',
    displayField: 'account_name',
    searchField: 'account_name',
    mockData: [
      { id: 'bank-001', name: 'Business Checking', code: '****1234', description: 'Primary business account' },
      { id: 'bank-002', name: 'Savings Account', code: '****5678', description: 'Business savings account' },
      { id: 'bank-003', name: 'Payroll Account', code: '****9012', description: 'Dedicated payroll account' },
      { id: 'bank-004', name: 'Investment Account', code: '****3456', description: 'Business investment account' },
    ]
  },
  tax_category: {
    name: 'Tax Category',
    icon: <Calculator className="h-4 w-4" />,
    apiEndpoint: '/api/tax-categories',
    displayField: 'name',
    searchField: 'name',
    mockData: [
      { id: 'tax-001', name: 'Sales Tax', code: 'ST', description: 'Standard sales tax' },
      { id: 'tax-002', name: 'Income Tax', code: 'IT', description: 'Corporate income tax' },
      { id: 'tax-003', name: 'Property Tax', code: 'PT', description: 'Real estate property tax' },
      { id: 'tax-004', name: 'Payroll Tax', code: 'PRT', description: 'Employee payroll taxes' },
    ]
  }
};

export function EntitySelector({
  integrationType,
  selectedEntityId,
  onEntitySelect,
  branchId
}: EntitySelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [entities, setEntities] = useState<Entity[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const config = integrationConfig[integrationType];

  useEffect(() => {
    if (!config) return;

    // For now, use mock data. In a real implementation, you would fetch from the API
    setIsLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
      setEntities(config.mockData);
      setIsLoading(false);
    }, 500);
  }, [integrationType, config]);

  const filteredEntities = entities.filter(entity =>
    entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (entity.code && entity.code.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const selectedEntity = entities.find(entity => entity.id === selectedEntityId);

  const handleViewEntity = () => {
    if (selectedEntity && config) {
      // Navigate to the entity's detail page
      const baseRoute = config.apiEndpoint.replace('/api/', '/');
      window.open(`${baseRoute}/${selectedEntity.id}`, '_blank');
    }
  };

  if (!config) {
    return (
      <div className="text-sm text-muted-foreground">
        Unknown integration type: {integrationType}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Integration Type Header */}
      <div className="flex items-center gap-2">
        {config.icon}
        <span className="font-medium">{config.name}</span>
        <Badge variant="outline">{entities.length} available</Badge>
      </div>

      {/* Search */}
      <div className="space-y-2">
        <Label>Search {config.name}</Label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={`Search ${config.name.toLowerCase()}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Entity Selection */}
      <div className="space-y-2">
        <Label>Select {config.name}</Label>
        <Select value={selectedEntityId} onValueChange={onEntitySelect}>
          <SelectTrigger>
            <SelectValue placeholder={`Choose ${config.name.toLowerCase()}`} />
          </SelectTrigger>
          <SelectContent>
            {isLoading ? (
              <SelectItem value="" disabled>
                Loading {config.name.toLowerCase()}s...
              </SelectItem>
            ) : filteredEntities.length > 0 ? (
              filteredEntities.map((entity) => (
                <SelectItem key={entity.id} value={entity.id}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex flex-col">
                      <span className="font-medium">{entity.name}</span>
                      {entity.code && (
                        <span className="text-xs text-muted-foreground">
                          Code: {entity.code}
                        </span>
                      )}
                      {entity.description && (
                        <span className="text-xs text-muted-foreground">
                          {entity.description}
                        </span>
                      )}
                    </div>
                  </div>
                </SelectItem>
              ))
            ) : (
              <SelectItem value="" disabled>
                No {config.name.toLowerCase()}s found
              </SelectItem>
            )}
          </SelectContent>
        </Select>
      </div>

      {/* Selected Entity Info */}
      {selectedEntity && (
        <div className="p-3 bg-muted rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium">{selectedEntity.name}</div>
              {selectedEntity.code && (
                <div className="text-sm text-muted-foreground">
                  Code: {selectedEntity.code}
                </div>
              )}
              {selectedEntity.description && (
                <div className="text-sm text-muted-foreground">
                  {selectedEntity.description}
                </div>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleViewEntity}
              className="flex items-center gap-1"
            >
              <ExternalLink className="h-3 w-3" />
              View
            </Button>
          </div>
        </div>
      )}

      {/* Integration Benefits */}
      <div className="text-xs text-muted-foreground">
        <strong>Integration Benefits:</strong>
        <ul className="list-disc list-inside mt-1 space-y-1">
          {integrationType === 'inventory' && (
            <>
              <li>Track inventory-related budget allocations</li>
              <li>Monitor stock investment and reorder costs</li>
              <li>Forecast seasonal demand impact on budget</li>
            </>
          )}
          {integrationType === 'customer' && (
            <>
              <li>Forecast revenue by customer segment</li>
              <li>Track customer acquisition costs</li>
              <li>Plan customer-specific marketing budgets</li>
            </>
          )}
          {integrationType === 'vendor' && (
            <>
              <li>Monitor vendor-specific spending</li>
              <li>Track contract renewal costs</li>
              <li>Optimize bulk purchase planning</li>
            </>
          )}
          {integrationType === 'project' && (
            <>
              <li>Allocate budget to specific projects</li>
              <li>Track project cost centers</li>
              <li>Monitor project profitability</li>
            </>
          )}
          {integrationType === 'employee' && (
            <>
              <li>Plan employee-specific costs</li>
              <li>Track training and development budgets</li>
              <li>Monitor compensation planning</li>
            </>
          )}
          {integrationType === 'asset' && (
            <>
              <li>Plan asset maintenance costs</li>
              <li>Track depreciation schedules</li>
              <li>Budget for asset replacements</li>
            </>
          )}
          {integrationType === 'bank_account' && (
            <>
              <li>Track account-specific cash flows</li>
              <li>Monitor banking fees and charges</li>
              <li>Plan account balance requirements</li>
            </>
          )}
          {integrationType === 'tax_category' && (
            <>
              <li>Plan tax provision requirements</li>
              <li>Track tax compliance costs</li>
              <li>Monitor tax optimization opportunities</li>
            </>
          )}
        </ul>
      </div>
    </div>
  );
}
