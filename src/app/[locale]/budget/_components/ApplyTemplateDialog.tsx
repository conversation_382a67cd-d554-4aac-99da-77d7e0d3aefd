'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';

// UI Components
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/safe-dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Calendar, AlertTriangle } from 'lucide-react';

// Types
import { BudgetTemplate } from '@/lib/types';

interface ApplyTemplateDialogProps {
  template?: BudgetTemplate | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApply: (year: number) => void;
  isLoading: boolean;
  defaultYear: number;
  defaultMonth: number;
}

export function ApplyTemplateDialog({
  template,
  open,
  onOpenChange,
  onApply,
  isLoading,
  defaultYear,
  defaultMonth
}: ApplyTemplateDialogProps) {
  const t = useTranslations('budget.templates.applyDialog');
  const [selectedYear, setSelectedYear] = useState<number>(defaultYear);

  // Reset selected year when dialog opens
  useEffect(() => {
    if (open) {
      setSelectedYear(defaultYear);
    }
  }, [open, defaultYear]);

  // Generate year options (current year ± 5 years)
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from(
    { length: 11 }, 
    (_, i) => currentYear - 5 + i
  );

  const handleApply = () => {
    onApply(selectedYear);
  };

  const handleClose = () => {
    setSelectedYear(defaultYear);
    onOpenChange(false);
  };

  if (!template) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {t('title')}
          </DialogTitle>
          <DialogDescription>
            {t('description', { templateName: template.name })}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Information */}
          <div className="rounded-lg border p-4 space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">{template.name}</h4>
              {template.isDefault && (
                <Badge variant="secondary">{t('defaultTemplate')}</Badge>
              )}
            </div>
            
            {template.description && (
              <p className="text-sm text-muted-foreground">
                {template.description}
              </p>
            )}
            
            <div className="flex items-center gap-4 text-sm">
              <span className="text-muted-foreground">
                {t('itemCount')}: <span className="font-medium">
                  {template.itemCount || template.items?.length || 0}
                </span>
              </span>
              <span className="text-muted-foreground">
                {t('status')}: <span className={`font-medium ${
                  template.isActive ? 'text-green-600' : 'text-gray-500'
                }`}>
                  {template.isActive ? t('active') : t('inactive')}
                </span>
              </span>
            </div>
          </div>

          {/* Year Selection */}
          <div className="space-y-2">
            <Label htmlFor="year-select">{t('selectYear')}</Label>
            <Select
              value={selectedYear.toString()}
              onValueChange={(value) => setSelectedYear(parseInt(value))}
            >
              <SelectTrigger id="year-select">
                <SelectValue placeholder={t('selectYearPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                {yearOptions.map((year) => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                    {year === currentYear && (
                      <span className="ml-2 text-xs text-muted-foreground">
                        ({t('currentYear')})
                      </span>
                    )}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Warning Alert */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {t('warning', { year: selectedYear })}
            </AlertDescription>
          </Alert>

          {/* Template Items Preview */}
          {template.items && template.items.length > 0 && (
            <div className="space-y-2">
              <Label>{t('templateItems')}</Label>
              <div className="rounded-lg border p-3 max-h-32 overflow-y-auto">
                <div className="space-y-1">
                  {template.items.slice(0, 5).map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="text-muted-foreground">
                        {item.accountId} {/* You might want to show account name instead */}
                      </span>
                      <span className="font-medium">
                        ${item.amount?.toLocaleString() || '0.00'}
                      </span>
                    </div>
                  ))}
                  {template.items.length > 5 && (
                    <div className="text-xs text-muted-foreground pt-1 border-t">
                      {t('andMoreItems', { count: template.items.length - 5 })}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Application Details */}
          <div className="rounded-lg bg-muted/50 p-3 space-y-2">
            <h5 className="font-medium text-sm">{t('applicationDetails')}</h5>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• {t('detail1')}</li>
              <li>• {t('detail2')}</li>
              <li>• {t('detail3')}</li>
            </ul>
          </div>
        </div>

        <DialogFooter>
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleClose}
            disabled={isLoading}
          >
            {t('cancel')}
          </Button>
          <Button 
            onClick={handleApply} 
            disabled={isLoading || !template.isActive}
          >
            {isLoading ? t('applying') : t('apply')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
