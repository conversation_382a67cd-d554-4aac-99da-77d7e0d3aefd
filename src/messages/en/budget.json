{"title": "Budget Management", "description": "Create and manage budgets for your organization", "tabs": {"budget": "Budget Items", "templates": "Templates", "reports": "Reports"}, "periodSelection": {"title": "Period Selection", "year": "Year", "month": "Month", "selectYear": "Select year", "selectMonth": "Select month"}, "budgetItems": {"title": "Budget Items", "add": "Add Budget Item", "edit": "Edit Budget Item", "delete": "Delete Budget Item", "noItems": "No budget items found for this period.", "account": "Account", "amount": "Amount", "notes": "Notes", "actions": "Actions", "selectAccount": "Select account", "loadingAccounts": "Loading accounts...", "noAccountsAvailable": "No accounts available", "createFor": "Create a new budget item for {month} {year}.", "updateFor": "Update budget item for {month} {year}.", "deleteConfirmation": "Are you sure you want to delete this budget item? This action cannot be undone.", "deleteWarning": "This will permanently remove the budget item from your records.", "adding": "Adding...", "updating": "Updating...", "deleting": "Deleting..."}, "templates": {"title": "Budget Templates", "description": "Description (Optional)", "createTemplate": "Create New Template", "saveAs": "Save as Template", "apply": "Apply", "edit": "Edit", "duplicate": "Duplicate", "delete": "Delete", "export": "Export", "actions": "Actions", "openMenu": "Open menu", "active": "Active", "inactive": "Inactive", "default": "<PERSON><PERSON><PERSON>", "noDescription": "No description", "searchPlaceholder": "Search templates...", "noTemplates": "No templates found.", "createFromCurrent": "Create a template from your current budget items by clicking \"Save as Template\" on the Budget Items tab.", "name": "Template Name", "setAsDefault": "Set as default template", "defaultTemplate": "<PERSON><PERSON><PERSON>", "items": "Items", "createTitle": "Save as Budget Template", "createDescription": "Create a reusable template from the current budget items.", "applyTitle": "Apply Budget Template", "applyDescription": "Apply this template to the selected period?", "applyWarning": "This will create or update budget items for {month} {year}.", "applyExistingWarning": "Existing budget items for the same accounts will be updated.", "deleteConfirmation": "Are you sure you want to delete this template?", "saving": "Saving...", "applying": "Applying...", "nameRequired": "Template name is required", "noItemsToTemplate": "No budget items to create template from", "bulkActions": "Bulk Actions", "exportAll": "Export All", "import": "Import Templates", "settings": "Template Settings", "table": {"title": "Budget Templates", "description": "Manage your reusable budget templates", "noTemplatesFound": "No templates found", "noTemplatesMatchingCriteria": "No templates match your search criteria", "columns": {"name": "Name", "description": "Description", "items": "Items", "status": "Status", "created": "Created", "actions": "Actions"}}, "createDialog": {"title": "Create Budget Template", "description": "Create a new reusable budget template"}, "editDialog": {"title": "Edit Budget Template", "description": "Update the budget template details"}, "deleteDialog": {"title": "Delete Template", "description": "Are you sure you want to delete the template \"{name}\"? This action cannot be undone."}, "form": {"name": {"label": "Template Name", "placeholder": "Enter template name", "description": "A descriptive name for your budget template"}, "description": {"label": "Description", "placeholder": "Enter template description (optional)", "description": "Optional description to help identify this template"}, "isDefault": {"label": "Set as <PERSON><PERSON><PERSON>", "description": "This template will be suggested when creating new budgets"}, "isActive": {"label": "Active Template", "description": "Only active templates can be applied to create budget items"}, "cancel": "Cancel", "create": "Create Template", "update": "Update Template", "creating": "Creating...", "updating": "Updating..."}, "applyDialog": {"title": "Apply Budget Template", "description": "Apply template \"{templateName}\" to create budget items", "selectYear": "Select Year", "selectYearPlaceholder": "Choose a year", "currentYear": "current", "warning": "This will create or update budget items for {year}. Existing items for the same accounts will be overwritten.", "templateItems": "Template Items", "andMoreItems": "and {count} more items...", "applicationDetails": "What will happen:", "detail1": "Budget items will be created for the selected year", "detail2": "Existing items for the same accounts will be updated", "detail3": "Template amounts will be applied to all 12 months", "cancel": "Cancel", "apply": "Apply Template", "applying": "Applying...", "defaultTemplate": "<PERSON><PERSON><PERSON>", "itemCount": "Items", "status": "Status", "active": "Active", "inactive": "Inactive"}}, "reports": {"title": "Budget Report", "budgetVsActual": "Budget vs. Actual", "category": "Category", "budgeted": "Budgeted", "actual": "Actual", "variance": "<PERSON><PERSON><PERSON>", "variancePercent": "Variance %", "revenue": "Revenue", "expenses": "Expenses", "net": "Net", "accountDetails": "Account Details", "accountType": "Type", "noReportData": "No budget report data available.", "createItemsForReport": "Create budget items for this period to generate a report.", "loadingChart": "Loading chart...", "loadingReport": "Loading report..."}, "import": {"title": "Import Budget Data", "description": "Paste CSV or JSON data to import budget items.", "placeholder": "Paste your CSV or JSON data here...", "button": "Import Budget", "importing": "Importing...", "noData": "No data to import", "failed": "Failed to import data"}, "export": {"button": "Export Budget"}, "errors": {"duplicateItem": "A budget item already exists for {account} in {month} {year}.", "duplicateWarning": "Warning: A budget item already exists for {account} in {month} {year}. Submitting will result in an error.", "editExisting": "Please edit the existing item instead.", "createFailed": "Failed to create budget item", "updateFailed": "Failed to update budget item", "deleteFailed": "Failed to delete budget item", "templateCreateFailed": "Failed to create template", "templateApplyFailed": "Failed to apply template"}, "success": {"itemCreated": "Budget item created successfully", "itemUpdated": "Budget item updated successfully", "itemDeleted": "Budget item deleted successfully", "templateCreated": "Template created successfully", "templateApplied": "Template applied successfully", "templateDeleted": "Template deleted successfully"}}