"use client"

import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { XIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface SafeDialogProps extends React.ComponentProps<typeof DialogPrimitive.Root> {
  onOpenChange?: (open: boolean) => void;
}

function SafeDialog({ onOpenChange, ...props }: SafeDialogProps) {
  const handleOpenChange = React.useCallback((open: boolean) => {
    if (!open) {
      // Immediate cleanup
      onOpenChange?.(false);
      
      // Force complete DOM reset after animation
      setTimeout(() => {
        // Remove all modal-related styles
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
        document.body.style.pointerEvents = '';
        document.documentElement.style.overflow = '';
        
        // Remove any modal classes
        document.body.classList.remove('overflow-hidden');
        
        // Clear any aria-hidden attributes
        document.querySelectorAll('[aria-hidden="true"]').forEach(el => {
          if (el !== document.body && !el.closest('[role="dialog"]')) {
            el.removeAttribute('aria-hidden');
          }
        });
        
        // Remove any portal containers that might be lingering
        document.querySelectorAll('[data-radix-portal]').forEach(portal => {
          try {
            if (portal.parentNode && !portal.querySelector('[role="dialog"]')) {
              portal.parentNode.removeChild(portal);
            }
          } catch (e) {
            // Ignore cleanup errors
          }
        });
        
        // Force focus to body and then to first interactive element
        document.body.focus();
        const firstInteractive = document.querySelector('button:not([disabled]), a[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled])') as HTMLElement;
        if (firstInteractive) {
          firstInteractive.focus();
          firstInteractive.blur();
        }
      }, 200);
    } else {
      onOpenChange?.(true);
    }
  }, [onOpenChange]);

  return <DialogPrimitive.Root {...props} onOpenChange={handleOpenChange} />
}

function SafeDialogTrigger({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {
  return <DialogPrimitive.Trigger {...props} />
}

function SafeDialogPortal({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Portal>) {
  return <DialogPrimitive.Portal {...props} />
}

function SafeDialogClose({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Close>) {
  return <DialogPrimitive.Close {...props} />
}

function SafeDialogOverlay({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {
  return (
    <DialogPrimitive.Overlay
      className={cn(
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",
        className
      )}
      {...props}
    />
  )
}

function SafeDialogContent({
  className,
  children,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Content>) {
  return (
    <SafeDialogPortal>
      <SafeDialogOverlay />
      <DialogPrimitive.Content
        className={cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",
          className
        )}
        {...props}
      >
        {children}
        <DialogPrimitive.Close className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4">
          <XIcon />
          <span className="sr-only">Close</span>
        </DialogPrimitive.Close>
      </DialogPrimitive.Content>
    </SafeDialogPortal>
  )
}

function SafeDialogHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      className={cn("flex flex-col gap-2 text-center sm:text-left", className)}
      {...props}
    />
  )
}

function SafeDialogFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      className={cn(
        "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",
        className
      )}
      {...props}
    />
  )
}

function SafeDialogTitle({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Title>) {
  return (
    <DialogPrimitive.Title
      className={cn("text-lg leading-none font-semibold", className)}
      {...props}
    />
  )
}

function SafeDialogDescription({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Description>) {
  return (
    <DialogPrimitive.Description
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  )
}

export {
  SafeDialog as Dialog,
  SafeDialogClose as DialogClose,
  SafeDialogContent as DialogContent,
  SafeDialogDescription as DialogDescription,
  SafeDialogFooter as DialogFooter,
  SafeDialogHeader as DialogHeader,
  SafeDialogOverlay as DialogOverlay,
  SafeDialogPortal as DialogPortal,
  SafeDialogTitle as DialogTitle,
  SafeDialogTrigger as DialogTrigger,
}
