'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  MoreHorizontal,
  Download, 
  Copy,
  Edit,
  Trash2,
  FileText,
  FileSpreadsheet,
  FileImage
} from 'lucide-react';
import { BudgetTemplate } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';
import { 
  useDeleteBudgetTemplateMutation,
  useDuplicateBudgetTemplateMutation,
  useExportBudgetTemplateMutation
} from '@/redux/services/budgetApi';
import { exportBudgetTemplate } from '@/lib/budget-utils';

interface BudgetTemplateActionsProps {
  template: BudgetTemplate;
  onEdit?: (template: BudgetTemplate) => void;
  onRefresh?: () => void;
}

export function BudgetTemplateActions({
  template,
  onEdit,
  onRefresh
}: BudgetTemplateActionsProps) {
  const t = useTranslations('budget');
  const common = useTranslations('common');
  const { toast } = useToast();

  // Dialog states
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDuplicateDialogOpen, setIsDuplicateDialogOpen] = useState(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);

  // Mutations
  const [deleteTemplate, { isLoading: isDeleting }] = useDeleteBudgetTemplateMutation();
  const [duplicateTemplate, { isLoading: isDuplicating }] = useDuplicateBudgetTemplateMutation();
  const [exportTemplate, { isLoading: isExporting }] = useExportBudgetTemplateMutation();

  // Form states
  const [duplicateName, setDuplicateName] = useState(`${template.name} (Copy)`);
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel' | 'pdf'>('csv');

  const handleDelete = async () => {
    try {
      await deleteTemplate(template.id).unwrap();
      toast({
        title: common('success'),
        description: 'Template deleted successfully',
      });
      onRefresh?.();
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error('Delete failed:', error);
      toast({
        title: common('error'),
        description: 'Failed to delete template',
        variant: 'destructive',
      });
    }
  };

  const handleDuplicate = async () => {
    try {
      await duplicateTemplate({ 
        id: template.id, 
        name: duplicateName 
      }).unwrap();
      toast({
        title: common('success'),
        description: 'Template duplicated successfully',
      });
      onRefresh?.();
      setIsDuplicateDialogOpen(false);
      setDuplicateName(`${template.name} (Copy)`);
    } catch (error) {
      console.error('Duplicate failed:', error);
      toast({
        title: common('error'),
        description: 'Failed to duplicate template',
        variant: 'destructive',
      });
    }
  };

  const handleExport = async () => {
    try {
      await exportBudgetTemplate(template, exportFormat);
      toast({
        title: common('success'),
        description: `Template exported as ${exportFormat.toUpperCase()}`,
      });
      setIsExportDialogOpen(false);
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: common('error'),
        description: 'Failed to export template',
        variant: 'destructive',
      });
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {onEdit && (
            <DropdownMenuItem onClick={() => onEdit(template)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Template
            </DropdownMenuItem>
          )}
          
          <DropdownMenuItem onClick={() => setIsDuplicateDialogOpen(true)}>
            <Copy className="mr-2 h-4 w-4" />
            Duplicate
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={() => setIsExportDialogOpen(true)}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            onClick={() => setIsDeleteDialogOpen(true)}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Template</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the template "{template.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <p className="text-amber-600">
              This will permanently remove the template and all its items. 
              Budget items created from this template will not be affected.
            </p>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete Template'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Duplicate Dialog */}
      <Dialog open={isDuplicateDialogOpen} onOpenChange={setIsDuplicateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Duplicate Template</DialogTitle>
            <DialogDescription>
              Create a copy of "{template.name}" with a new name.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="duplicate-name">New Template Name</Label>
              <Input
                id="duplicate-name"
                value={duplicateName}
                onChange={(e) => setDuplicateName(e.target.value)}
                placeholder="Enter name for the duplicate template"
              />
            </div>

            <div className="text-sm text-muted-foreground">
              The duplicate will include all items from the original template with the same amounts and notes.
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDuplicateDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleDuplicate}
              disabled={isDuplicating || !duplicateName.trim()}
            >
              {isDuplicating ? 'Duplicating...' : 'Create Duplicate'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export Dialog */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Template</DialogTitle>
            <DialogDescription>
              Export "{template.name}" template in your preferred format.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="export-format">Export Format</Label>
              <Select
                value={exportFormat}
                onValueChange={(value: 'csv' | 'excel' | 'pdf') => setExportFormat(value)}
              >
                <SelectTrigger id="export-format">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">
                    <div className="flex items-center">
                      <FileText className="mr-2 h-4 w-4" />
                      CSV - Comma Separated Values
                    </div>
                  </SelectItem>
                  <SelectItem value="excel">
                    <div className="flex items-center">
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      Excel - Spreadsheet
                    </div>
                  </SelectItem>
                  <SelectItem value="pdf">
                    <div className="flex items-center">
                      <FileImage className="mr-2 h-4 w-4" />
                      PDF - Portable Document
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="text-sm text-muted-foreground">
              The export will include template metadata (name, description, creation date) 
              and all template items with their account IDs, amounts, and notes.
            </div>

            <div className="bg-blue-50 p-3 rounded-md">
              <h4 className="font-medium text-blue-900 mb-1">Template Summary</h4>
              <div className="text-sm text-blue-700">
                <p>Items: {template.items?.length || template.itemCount || 0}</p>
                <p>Created: {new Date(template.createdAt).toLocaleDateString()}</p>
                {template.isDefault && <p className="font-medium">Default Template</p>}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting}
            >
              {isExporting ? 'Exporting...' : `Export as ${exportFormat.toUpperCase()}`}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
