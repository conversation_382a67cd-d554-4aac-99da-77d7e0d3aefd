'use client';

import React, { useState, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  RefreshCw,
  Download
} from 'lucide-react';
import { useI18n } from '@/hooks/useI18n';
import { useBranchContext, usePermissionContext } from '@/contexts/BranchContext';
import { useGetBudgetComparisonQuery } from '@/redux/services/budgetApi';
import { formatBudgetAmount, calculateBudgetVariance } from '@/lib/budget-utils';

interface BudgetComparisonProps {
  currentYear: number;
  currentMonth: number;
}

export function BudgetComparison({ currentYear, currentMonth }: BudgetComparisonProps) {
  const t = useTranslations('budget');
  const common = useTranslations('common');
  const { formatCurrency } = useI18n();

  // Branch context
  const { selectedOrganizationId, selectedBranchId, hasPermissionContext } = useBranchContext();
  const { addContextToParams } = usePermissionContext();
  const hasContext = hasPermissionContext();

  // Comparison period state
  const [comparisonPeriod, setComparisonPeriod] = useState<'previous-month' | 'previous-year' | 'custom'>('previous-month');
  const [customYear, setCustomYear] = useState(currentYear - 1);
  const [customMonth, setCustomMonth] = useState(currentMonth);

  // Calculate comparison period
  const { previousYear, previousMonth } = useMemo(() => {
    switch (comparisonPeriod) {
      case 'previous-month':
        if (currentMonth === 1) {
          return { previousYear: currentYear - 1, previousMonth: 12 };
        }
        return { previousYear: currentYear, previousMonth: currentMonth - 1 };
      
      case 'previous-year':
        return { previousYear: currentYear - 1, previousMonth: currentMonth };
      
      case 'custom':
        return { previousYear: customYear, previousMonth: customMonth };
      
      default:
        return { previousYear: currentYear, previousMonth: currentMonth - 1 };
    }
  }, [comparisonPeriod, currentYear, currentMonth, customYear, customMonth]);

  // Build query parameters
  const comparisonParams = useMemo(() => {
    const baseParams = {
      currentYear,
      currentMonth,
      previousYear,
      previousMonth
    };
    return hasContext ? addContextToParams(baseParams) : baseParams;
  }, [hasContext, addContextToParams, currentYear, currentMonth, previousYear, previousMonth]);

  // Fetch comparison data
  const { 
    data: comparisonData, 
    isLoading, 
    error,
    refetch 
  } = useGetBudgetComparisonQuery(comparisonParams);

  const getVarianceIcon = (variance: number) => {
    if (variance > 0) return <TrendingUp className="h-4 w-4 text-red-500" />;
    if (variance < 0) return <TrendingDown className="h-4 w-4 text-green-500" />;
    return <Minus className="h-4 w-4 text-gray-500" />;
  };

  const getVarianceBadge = (variancePercent: number) => {
    const absPercent = Math.abs(variancePercent);
    if (absPercent < 5) {
      return <Badge variant="secondary">On Target</Badge>;
    } else if (variancePercent > 0) {
      return <Badge variant="destructive">Over Budget</Badge>;
    } else {
      return <Badge variant="default">Under Budget</Badge>;
    }
  };

  const formatPeriod = (year: number, month: number) => {
    return new Date(year, month - 1).toLocaleDateString('default', {
      month: 'long',
      year: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget Comparison</CardTitle>
          <CardDescription>Loading comparison data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-16 bg-gray-100 dark:bg-gray-800 rounded animate-pulse" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget Comparison</CardTitle>
          <CardDescription>Failed to load comparison data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">Unable to load budget comparison</p>
            <Button onClick={() => refetch()} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Budget Comparison</CardTitle>
            <CardDescription>
              Compare budget between {formatPeriod(currentYear, currentMonth)} and {formatPeriod(previousYear, previousMonth)}
            </CardDescription>
          </div>
          <Button onClick={() => refetch()} variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Period Selection */}
        <div className="flex flex-wrap gap-4">
          <div className="space-y-2">
            <Label>Compare with</Label>
            <Select value={comparisonPeriod} onValueChange={(value: any) => setComparisonPeriod(value)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="previous-month">Previous Month</SelectItem>
                <SelectItem value="previous-year">Same Month Last Year</SelectItem>
                <SelectItem value="custom">Custom Period</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {comparisonPeriod === 'custom' && (
            <>
              <div className="space-y-2">
                <Label>Year</Label>
                <Select value={customYear.toString()} onValueChange={(value) => setCustomYear(parseInt(value))}>
                  <SelectTrigger className="w-24">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 5 }, (_, i) => currentYear - i).map(year => (
                      <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Month</Label>
                <Select value={customMonth.toString()} onValueChange={(value) => setCustomMonth(parseInt(value))}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                      <SelectItem key={month} value={month.toString()}>
                        {new Date(2000, month - 1).toLocaleDateString('default', { month: 'long' })}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </div>

        {comparisonData && (
          <>
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Current Period</p>
                      <p className="text-2xl font-bold">{formatCurrency(comparisonData.currentPeriod.total)}</p>
                      <p className="text-xs text-muted-foreground">{formatPeriod(currentYear, currentMonth)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Previous Period</p>
                      <p className="text-2xl font-bold">{formatCurrency(comparisonData.previousPeriod.total)}</p>
                      <p className="text-xs text-muted-foreground">{formatPeriod(previousYear, previousMonth)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Variance</p>
                      <div className="flex items-center gap-2">
                        {getVarianceIcon(comparisonData.variance)}
                        <p className="text-2xl font-bold">
                          {formatCurrency(Math.abs(comparisonData.variance))}
                        </p>
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="text-xs text-muted-foreground">
                          {comparisonData.variancePercent.toFixed(1)}%
                        </p>
                        {getVarianceBadge(comparisonData.variancePercent)}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Account-level Comparison */}
            {comparisonData.items && comparisonData.items.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-4">Account Comparison</h3>
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Account</TableHead>
                        <TableHead className="text-right">Current</TableHead>
                        <TableHead className="text-right">Previous</TableHead>
                        <TableHead className="text-right">Variance</TableHead>
                        <TableHead className="text-right">%</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {comparisonData.items.map((item) => (
                        <TableRow key={item.accountId}>
                          <TableCell className="font-medium">{item.accountName}</TableCell>
                          <TableCell className="text-right font-mono">
                            {formatCurrency(item.current)}
                          </TableCell>
                          <TableCell className="text-right font-mono">
                            {formatCurrency(item.previous)}
                          </TableCell>
                          <TableCell className="text-right font-mono">
                            <div className="flex items-center justify-end gap-1">
                              {getVarianceIcon(item.variance)}
                              {formatCurrency(Math.abs(item.variance))}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            {item.variancePercent.toFixed(1)}%
                          </TableCell>
                          <TableCell>
                            {getVarianceBadge(item.variancePercent)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
