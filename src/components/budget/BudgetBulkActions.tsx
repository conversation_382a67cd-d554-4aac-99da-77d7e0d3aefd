'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Trash2, 
  Edit, 
  Download, 
  X,
  FileText,
  FileSpreadsheet,
  FileImage
} from 'lucide-react';
import { BudgetItem } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';
import { 
  useBulkDeleteBudgetItemsMutation,
  useBulkUpdateBudgetItemsMutation,
  useExportBudgetItemsMutation
} from '@/redux/services/budgetApi';
import { exportBudgetItems } from '@/lib/budget-utils';

interface BudgetBulkActionsProps {
  selectedIds: string[];
  selectedItems: BudgetItem[];
  totalCount: number;
  onClearSelection: () => void;
  onRefresh?: () => void;
}

export function BudgetBulkActions({
  selectedIds,
  selectedItems,
  totalCount,
  onClearSelection,
  onRefresh
}: BudgetBulkActionsProps) {
  const t = useTranslations('budget');
  const common = useTranslations('common');
  const { toast } = useToast();

  // Dialog states
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);

  // Mutations
  const [bulkDelete, { isLoading: isDeleting }] = useBulkDeleteBudgetItemsMutation();
  const [bulkUpdate, { isLoading: isUpdating }] = useBulkUpdateBudgetItemsMutation();
  const [exportItems, { isLoading: isExporting }] = useExportBudgetItemsMutation();

  // Edit form state
  const [editForm, setEditForm] = useState({
    amount: '',
    notes: '',
    updateAmount: false,
    updateNotes: false
  });

  // Export form state
  const [exportForm, setExportForm] = useState({
    format: 'csv' as 'csv' | 'excel' | 'pdf',
    includeAccountDetails: true,
    includePeriodInfo: true
  });

  const selectedCount = selectedIds.length;

  if (selectedCount === 0) {
    return null;
  }

  const handleBulkDelete = async () => {
    try {
      await bulkDelete(selectedIds).unwrap();
      toast({
        title: common('success'),
        description: `${selectedCount} budget items deleted successfully`,
      });
      onClearSelection();
      onRefresh?.();
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error('Bulk delete failed:', error);
      toast({
        title: common('error'),
        description: 'Failed to delete budget items',
        variant: 'destructive',
      });
    }
  };

  const handleBulkEdit = async () => {
    try {
      const updates: Partial<BudgetItem> = {};
      
      if (editForm.updateAmount && editForm.amount) {
        updates.amount = parseFloat(editForm.amount);
      }
      
      if (editForm.updateNotes) {
        updates.notes = editForm.notes;
      }

      if (Object.keys(updates).length === 0) {
        toast({
          title: common('error'),
          description: 'Please select at least one field to update',
          variant: 'destructive',
        });
        return;
      }

      await bulkUpdate({ ids: selectedIds, updates }).unwrap();
      toast({
        title: common('success'),
        description: `${selectedCount} budget items updated successfully`,
      });
      onClearSelection();
      onRefresh?.();
      setIsEditDialogOpen(false);
      setEditForm({
        amount: '',
        notes: '',
        updateAmount: false,
        updateNotes: false
      });
    } catch (error) {
      console.error('Bulk update failed:', error);
      toast({
        title: common('error'),
        description: 'Failed to update budget items',
        variant: 'destructive',
      });
    }
  };

  const handleBulkExport = async () => {
    try {
      await exportBudgetItems(selectedItems, {
        format: exportForm.format,
        includeAccountDetails: exportForm.includeAccountDetails,
        includePeriodInfo: exportForm.includePeriodInfo,
        filename: `selected-budget-items-${new Date().toISOString().split('T')[0]}`
      });

      toast({
        title: common('success'),
        description: `${selectedCount} budget items exported successfully`,
      });
      setIsExportDialogOpen(false);
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: common('error'),
        description: 'Failed to export budget items',
        variant: 'destructive',
      });
    }
  };

  return (
    <>
      <div className="flex items-center justify-between p-3 bg-muted rounded-lg border">
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="font-medium">
            {selectedCount} of {totalCount} selected
          </Badge>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExportDialogOpen(true)}
              disabled={isExporting}
            >
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditDialogOpen(true)}
              disabled={isUpdating}
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>

            <Button
              variant="destructive"
              size="sm"
              onClick={() => setIsDeleteDialogOpen(true)}
              disabled={isDeleting}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={onClearSelection}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Budget Items</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedCount} budget items? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <p className="text-amber-600">This will permanently remove the selected budget items from your records.</p>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleBulkDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : `Delete ${selectedCount} Items`}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Budget Items</DialogTitle>
            <DialogDescription>
              Update {selectedCount} budget items. Only checked fields will be updated.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="update-amount"
                checked={editForm.updateAmount}
                onChange={(e) => setEditForm({...editForm, updateAmount: e.target.checked})}
              />
              <Label htmlFor="update-amount">Update Amount</Label>
            </div>
            
            {editForm.updateAmount && (
              <div className="space-y-2">
                <Label htmlFor="bulk-amount">New Amount</Label>
                <Input
                  id="bulk-amount"
                  type="number"
                  step="0.01"
                  value={editForm.amount}
                  onChange={(e) => setEditForm({...editForm, amount: e.target.value})}
                  placeholder="Enter new amount"
                />
              </div>
            )}

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="update-notes"
                checked={editForm.updateNotes}
                onChange={(e) => setEditForm({...editForm, updateNotes: e.target.checked})}
              />
              <Label htmlFor="update-notes">Update Notes</Label>
            </div>
            
            {editForm.updateNotes && (
              <div className="space-y-2">
                <Label htmlFor="bulk-notes">Notes</Label>
                <Textarea
                  id="bulk-notes"
                  value={editForm.notes}
                  onChange={(e) => setEditForm({...editForm, notes: e.target.value})}
                  placeholder="Enter notes (leave empty to clear)"
                  rows={3}
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleBulkEdit}
              disabled={isUpdating}
            >
              {isUpdating ? 'Updating...' : `Update ${selectedCount} Items`}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export Dialog */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Budget Items</DialogTitle>
            <DialogDescription>
              Export {selectedCount} selected budget items in your preferred format.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="export-format">Export Format</Label>
              <Select
                value={exportForm.format}
                onValueChange={(value: 'csv' | 'excel' | 'pdf') => 
                  setExportForm({...exportForm, format: value})
                }
              >
                <SelectTrigger id="export-format">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">
                    <div className="flex items-center">
                      <FileText className="mr-2 h-4 w-4" />
                      CSV
                    </div>
                  </SelectItem>
                  <SelectItem value="excel">
                    <div className="flex items-center">
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      Excel
                    </div>
                  </SelectItem>
                  <SelectItem value="pdf">
                    <div className="flex items-center">
                      <FileImage className="mr-2 h-4 w-4" />
                      PDF
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label>Include Options</Label>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="include-account-details"
                  checked={exportForm.includeAccountDetails}
                  onChange={(e) => setExportForm({...exportForm, includeAccountDetails: e.target.checked})}
                />
                <Label htmlFor="include-account-details">Account Details (Name & Code)</Label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="include-period-info"
                  checked={exportForm.includePeriodInfo}
                  onChange={(e) => setExportForm({...exportForm, includePeriodInfo: e.target.checked})}
                />
                <Label htmlFor="include-period-info">Period Information</Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleBulkExport}
              disabled={isExporting}
            >
              {isExporting ? 'Exporting...' : `Export ${selectedCount} Items`}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
