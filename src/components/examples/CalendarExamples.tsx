'use client';

import React, { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { DatePicker } from '@/components/ui/date-picker';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

export function CalendarExamples() {
  // Single date selection
  const [singleDate, setSingleDate] = useState<Date | undefined>(new Date());
  
  // Multiple dates selection
  const [multipleDates, setMultipleDates] = useState<Date[] | undefined>([]);
  
  // Date range selection
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  });

  // Date picker state
  const [pickerDate, setPickerDate] = useState<Date | undefined>();

  return (
    <div className="space-y-8 p-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* 1. Basic Single Date Calendar */}
        <Card>
          <CardHeader>
            <CardTitle>Single Date Selection</CardTitle>
            <CardDescription>Select a single date</CardDescription>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={singleDate}
              onSelect={setSingleDate}
              className="rounded-md border"
            />
            <p className="mt-4 text-sm text-muted-foreground">
              Selected: {singleDate ? format(singleDate, 'PPP') : 'No date selected'}
            </p>
          </CardContent>
        </Card>

        {/* 2. Multiple Dates Calendar */}
        <Card>
          <CardHeader>
            <CardTitle>Multiple Dates Selection</CardTitle>
            <CardDescription>Select multiple dates</CardDescription>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="multiple"
              selected={multipleDates}
              onSelect={setMultipleDates}
              className="rounded-md border"
            />
            <p className="mt-4 text-sm text-muted-foreground">
              Selected: {multipleDates?.length || 0} dates
            </p>
          </CardContent>
        </Card>

        {/* 3. Date Range Calendar */}
        <Card>
          <CardHeader>
            <CardTitle>Date Range Selection</CardTitle>
            <CardDescription>Select a date range</CardDescription>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="range"
              selected={dateRange}
              onSelect={setDateRange}
              className="rounded-md border"
              numberOfMonths={2}
            />
            <p className="mt-4 text-sm text-muted-foreground">
              From: {dateRange.from ? format(dateRange.from, 'PPP') : 'Not selected'}<br />
              To: {dateRange.to ? format(dateRange.to, 'PPP') : 'Not selected'}
            </p>
          </CardContent>
        </Card>

        {/* 4. Date Picker (Popover) */}
        <Card>
          <CardHeader>
            <CardTitle>Date Picker</CardTitle>
            <CardDescription>Date picker with popover</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Using the existing DatePicker component */}
            <div>
              <label className="text-sm font-medium">Using DatePicker Component:</label>
              <DatePicker
                date={pickerDate}
                setDate={setPickerDate}
                placeholder="Select a date"
                className="mt-2"
              />
            </div>

            {/* Custom popover implementation */}
            <div>
              <label className="text-sm font-medium">Custom Popover Implementation:</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal mt-2",
                      !singleDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {singleDate ? format(singleDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={singleDate}
                    onSelect={setSingleDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </CardContent>
        </Card>

        {/* 5. Calendar with Disabled Dates */}
        <Card>
          <CardHeader>
            <CardTitle>Calendar with Disabled Dates</CardTitle>
            <CardDescription>Disable past dates and weekends</CardDescription>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={singleDate}
              onSelect={setSingleDate}
              disabled={(date) => 
                date < new Date() || // Disable past dates
                date.getDay() === 0 || date.getDay() === 6 // Disable weekends
              }
              className="rounded-md border"
            />
          </CardContent>
        </Card>

        {/* 6. Calendar with Custom Styling */}
        <Card>
          <CardHeader>
            <CardTitle>Custom Styled Calendar</CardTitle>
            <CardDescription>Calendar with custom class names</CardDescription>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={singleDate}
              onSelect={setSingleDate}
              className="rounded-md border"
              classNames={{
                day_selected: "bg-blue-500 text-white hover:bg-blue-600",
                day_today: "bg-yellow-100 text-yellow-900",
              }}
            />
          </CardContent>
        </Card>
      </div>

      {/* 7. Calendar with Form Integration */}
      <Card>
        <CardHeader>
          <CardTitle>Calendar in Forms</CardTitle>
          <CardDescription>How to use calendar in forms with validation</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Start Date</label>
                <DatePicker
                  date={dateRange.from}
                  setDate={(date) => setDateRange(prev => ({ ...prev, from: date }))}
                  placeholder="Select start date"
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">End Date</label>
                <DatePicker
                  date={dateRange.to}
                  setDate={(date) => setDateRange(prev => ({ ...prev, to: date }))}
                  placeholder="Select end date"
                  className="mt-1"
                />
              </div>
            </div>
            
            <Button 
              onClick={() => {
                if (dateRange.from && dateRange.to) {
                  alert(`Selected range: ${format(dateRange.from, 'PPP')} to ${format(dateRange.to, 'PPP')}`);
                } else {
                  alert('Please select both start and end dates');
                }
              }}
            >
              Submit Date Range
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
