'use client';

import React, { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { DatePicker } from '@/components/ui/date-picker';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Code2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

export function CalendarUsageGuide() {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  });

  const codeExamples = {
    basic: `import { Calendar } from '@/components/ui/calendar';

function BasicCalendar() {
  const [date, setDate] = useState<Date | undefined>(new Date());
  
  return (
    <Calendar
      mode="single"
      selected={date}
      onSelect={setDate}
      className="rounded-md border"
    />
  );
}`,

    datePicker: `import { DatePicker } from '@/components/ui/date-picker';

function DatePickerExample() {
  const [date, setDate] = useState<Date | undefined>();
  
  return (
    <DatePicker
      date={date}
      setDate={setDate}
      placeholder="Select a date"
      className="w-full"
    />
  );
}`,

    range: `import { Calendar } from '@/components/ui/calendar';

function DateRangeCalendar() {
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: undefined, to: undefined });
  
  return (
    <Calendar
      mode="range"
      selected={dateRange}
      onSelect={setDateRange}
      numberOfMonths={2}
      className="rounded-md border"
    />
  );
}`,

    form: `import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const schema = z.object({
  date: z.date({ required_error: 'Date is required' }),
});

function FormWithCalendar() {
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: { date: new Date() }
  });

  return (
    <FormField
      control={form.control}
      name="date"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Date</FormLabel>
          <FormControl>
            <DatePicker
              date={field.value}
              setDate={field.onChange}
              placeholder="Select date"
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}`,

    disabled: `<Calendar
  mode="single"
  selected={date}
  onSelect={setDate}
  disabled={(date) => 
    date < new Date() || // Disable past dates
    date.getDay() === 0 || date.getDay() === 6 // Disable weekends
  }
  className="rounded-md border"
/>`
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold">Shadcn Calendar Usage Guide</h1>
        <p className="text-muted-foreground text-lg">
          Complete guide to using calendar components in your React application
        </p>
      </div>

      {/* Quick Reference */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code2 className="h-5 w-5" />
            Quick Reference
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Badge variant="outline">Single Date</Badge>
              <code className="text-sm">mode="single"</code>
            </div>
            <div className="space-y-2">
              <Badge variant="outline">Multiple Dates</Badge>
              <code className="text-sm">mode="multiple"</code>
            </div>
            <div className="space-y-2">
              <Badge variant="outline">Date Range</Badge>
              <code className="text-sm">mode="range"</code>
            </div>
            <div className="space-y-2">
              <Badge variant="outline">With Popover</Badge>
              <code className="text-sm">DatePicker</code>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Examples Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* 1. Basic Calendar */}
        <Card>
          <CardHeader>
            <CardTitle>1. Basic Calendar</CardTitle>
            <CardDescription>Simple single date selection</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              className="rounded-md border mx-auto"
            />
            <div className="text-sm text-muted-foreground">
              Selected: {date ? format(date, 'PPP') : 'No date selected'}
            </div>
            <details className="text-xs">
              <summary className="cursor-pointer font-medium">Show Code</summary>
              <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
                <code>{codeExamples.basic}</code>
              </pre>
            </details>
          </CardContent>
        </Card>

        {/* 2. Date Picker */}
        <Card>
          <CardHeader>
            <CardTitle>2. Date Picker (Popover)</CardTitle>
            <CardDescription>Calendar in a popover trigger</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <DatePicker
              date={date}
              setDate={setDate}
              placeholder="Pick a date"
              className="w-full"
            />
            <details className="text-xs">
              <summary className="cursor-pointer font-medium">Show Code</summary>
              <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
                <code>{codeExamples.datePicker}</code>
              </pre>
            </details>
          </CardContent>
        </Card>

        {/* 3. Date Range */}
        <Card>
          <CardHeader>
            <CardTitle>3. Date Range Selection</CardTitle>
            <CardDescription>Select start and end dates</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Calendar
              mode="range"
              selected={dateRange}
              onSelect={setDateRange}
              numberOfMonths={2}
              className="rounded-md border"
            />
            <div className="text-sm text-muted-foreground">
              From: {dateRange.from ? format(dateRange.from, 'PPP') : 'Not selected'}<br />
              To: {dateRange.to ? format(dateRange.to, 'PPP') : 'Not selected'}
            </div>
            <details className="text-xs">
              <summary className="cursor-pointer font-medium">Show Code</summary>
              <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
                <code>{codeExamples.range}</code>
              </pre>
            </details>
          </CardContent>
        </Card>

        {/* 4. Disabled Dates */}
        <Card>
          <CardHeader>
            <CardTitle>4. Calendar with Disabled Dates</CardTitle>
            <CardDescription>Disable past dates and weekends</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              disabled={(date) => 
                date < new Date() || // Disable past dates
                date.getDay() === 0 || date.getDay() === 6 // Disable weekends
              }
              className="rounded-md border mx-auto"
            />
            <details className="text-xs">
              <summary className="cursor-pointer font-medium">Show Code</summary>
              <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
                <code>{codeExamples.disabled}</code>
              </pre>
            </details>
          </CardContent>
        </Card>
      </div>

      {/* Form Integration */}
      <Card>
        <CardHeader>
          <CardTitle>5. Form Integration with React Hook Form</CardTitle>
          <CardDescription>How to use calendar in forms with validation</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 border rounded-lg bg-muted/50">
            <p className="text-sm text-muted-foreground mb-2">
              This is how the calendar is used in the expense form:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <DatePicker
                date={date}
                setDate={setDate}
                placeholder="Select expense date"
                className="w-full"
              />
              <DatePicker
                date={dateRange.from}
                setDate={(date) => setDateRange(prev => ({ ...prev, from: date }))}
                placeholder="Select start date"
                className="w-full"
              />
            </div>
          </div>
          <details className="text-xs">
            <summary className="cursor-pointer font-medium">Show Form Code</summary>
            <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
              <code>{codeExamples.form}</code>
            </pre>
          </details>
        </CardContent>
      </Card>

      {/* Props Reference */}
      <Card>
        <CardHeader>
          <CardTitle>Calendar Props Reference</CardTitle>
          <CardDescription>Common props and their usage</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Prop</th>
                  <th className="text-left p-2">Type</th>
                  <th className="text-left p-2">Description</th>
                </tr>
              </thead>
              <tbody className="text-xs">
                <tr className="border-b">
                  <td className="p-2"><code>mode</code></td>
                  <td className="p-2">"single" | "multiple" | "range"</td>
                  <td className="p-2">Selection mode</td>
                </tr>
                <tr className="border-b">
                  <td className="p-2"><code>selected</code></td>
                  <td className="p-2">Date | Date[] | DateRange</td>
                  <td className="p-2">Selected date(s)</td>
                </tr>
                <tr className="border-b">
                  <td className="p-2"><code>onSelect</code></td>
                  <td className="p-2">Function</td>
                  <td className="p-2">Selection handler</td>
                </tr>
                <tr className="border-b">
                  <td className="p-2"><code>disabled</code></td>
                  <td className="p-2">Function | Date[]</td>
                  <td className="p-2">Disable specific dates</td>
                </tr>
                <tr className="border-b">
                  <td className="p-2"><code>numberOfMonths</code></td>
                  <td className="p-2">number</td>
                  <td className="p-2">Number of months to display</td>
                </tr>
                <tr className="border-b">
                  <td className="p-2"><code>className</code></td>
                  <td className="p-2">string</td>
                  <td className="p-2">CSS classes</td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
