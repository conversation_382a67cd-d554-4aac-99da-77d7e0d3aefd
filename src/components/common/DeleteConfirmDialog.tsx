'use client';

import React from 'react';
import { DeleteDialog } from '@/components/ui/delete-dialog';

interface DeleteConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  isLoading?: boolean;
  title: string;
  description: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  warningMessage?: string;
  itemDetails?: React.ReactNode;
}

export function DeleteConfirmDialog({
  open,
  onOpenChange,
  onConfirm,
  isLoading,
  title,
  description,
  confirmButtonText = 'Delete',
  cancelButtonText = 'Cancel',
  warningMessage,
  itemDetails
}: DeleteConfirmDialogProps) {
  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <DeleteDialog
      isOpen={open}
      onClose={handleClose}
      onConfirm={onConfirm}
      title={title}
      description={description}
      confirmButtonText={confirmButtonText}
      cancelButtonText={cancelButtonText}
      loadingText="Deleting..."
      successMessage="Item deleted successfully"
      errorMessage="Failed to delete item"
      warningMessage={warningMessage}
      itemDetails={itemDetails}
    />
  );
}
