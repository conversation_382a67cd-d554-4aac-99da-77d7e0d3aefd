'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { DatePicker } from '@/components/ui/date-picker';

// API Hooks
import { useGetVendorsQuery } from '@/redux/services/vendorsApi';
import { useGetExpenseCategoriesByBranchQuery } from '@/redux/services/expenseCategoriesApi';
import { useCreateExpenseMutation, useUpdateExpenseMutation, type Expense } from '@/redux/services/expensesApi';
import { useGetAccountsQuery } from '@/redux/services/chartOfAccountsApi';

// Simplified schema - relationships now handled by integration system
const expenseSchema = z.object({
  // Core required fields
  description: z.string().min(1, 'Description is required'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  expenseDate: z.date({ required_error: 'Expense date is required' }),

  // Required relationships - only essential fields for basic expense creation
  accountId: z.string().min(1, 'Chart of Account is required'), // ✅ Required - for proper accounting

  // Basic expense fields (kept for compatibility with current API)
  vendorId: z.string().optional(), // ✅ Keep - basic vendor info still useful
  category: z.string().optional(), // ✅ Keep - basic categorization
  paymentMethod: z.enum(['cash', 'credit_card', 'debit_card', 'bank_transfer', 'check', 'online_payment']).optional(),

  // Additional fields
  receiptUrl: z.string().url('Invalid URL').optional().or(z.literal('')),
  notes: z.string().optional(),

  // Status and approval fields (for editing)
  status: z.enum(['PENDING', 'APPROVED', 'REJECTED', 'PAID']),
  approvedBy: z.string().optional(),
  approvedAt: z.string().optional(),
  paidAt: z.string().optional(),
});

type ExpenseFormData = z.infer<typeof expenseSchema>;

interface ExpenseFormProps {
  expense?: Expense;
  branchId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ExpenseFormSimplified({ expense, branchId, onSuccess, onCancel }: ExpenseFormProps) {
  const { toast } = useToast();

  // API queries for essential data only
  const { data: vendors } = useGetVendorsQuery();
  const { data: expenseCategories } = useGetExpenseCategoriesByBranchQuery(branchId);
  const { data: accounts, isLoading: accountsLoading, error: accountsError } = useGetAccountsQuery({
    branchId: branchId,
    limit: 100, // Get more accounts for selection
    isActive: true // Only active accounts
  });

  // Mutations
  const [createExpense, { isLoading: isCreating }] = useCreateExpenseMutation();
  const [updateExpense, { isLoading: isUpdating }] = useUpdateExpenseMutation();

  const form = useForm<ExpenseFormData>({
    resolver: zodResolver(expenseSchema),
    mode: 'onChange',
    defaultValues: {
      // Core required fields
      description: '',
      amount: 0,
      expenseDate: new Date(),
      accountId: '', // Required

      // Optional commonly used fields
      vendorId: '',
      category: '',
      paymentMethod: undefined,

      // Additional fields
      receiptUrl: '',
      notes: '',

      // Status and approval fields
      status: 'PENDING' as const,
      approvedBy: '',
      approvedAt: '',
      paidAt: '',
    },
  });

  // Populate form when editing
  useEffect(() => {
    if (expense) {
      form.reset({
        description: expense.description,
        amount: expense.amount,
        expenseDate: new Date(expense.date),
        vendorId: expense.vendor_id || '',
        category: expense.category || '',
        paymentMethod: (expense.payment_method as any) || 'cash',
        receiptUrl: expense.receipt_url || '',
        notes: '', // Note: Expense type doesn't have notes field
        status: expense.status,
        accountId: '', // Will need to be set based on expense data
        approvedBy: '',
        approvedAt: '',
        paidAt: '',
      });
    }
  }, [expense, form]);

  const onSubmit = async (data: ExpenseFormData) => {
    try {
      if (expense) {
        // For updates, match the UpdateExpenseRequest interface
        const updateData = {
          description: data.description,
          amount: data.amount,
          date: data.expenseDate.toISOString(),
          vendor_id: data.vendorId || '',
          category: data.category || 'General',
          payment_method: data.paymentMethod || 'cash',
          expenseDate: data.expenseDate.toISOString(),
          notes: data.notes || null,
          receiptUrl: data.receiptUrl || null,
          status: data.status,
        };
        await updateExpense({ id: expense.id, body: updateData }).unwrap();
        toast({
          title: 'Success',
          description: 'Expense updated successfully',
        });
      } else {
        // For creation, match the CreateExpenseRequest interface
        const createData = {
          date: data.expenseDate.toISOString(),
          vendor_id: data.vendorId || 'default-vendor', // Backend requires vendor_id
          category: data.category || 'General', // Backend requires category
          description: data.description,
          amount: data.amount,
          payment_method: data.paymentMethod || 'cash',
          receipt_url: data.receiptUrl || undefined,
          branch_id: branchId, // Include branch ID for backend
          account_id: data.accountId, // Include account ID for backend
        };
        await createExpense(createData).unwrap();
        toast({
          title: 'Success',
          description: 'Expense created successfully',
        });
      }
      onSuccess?.();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save expense',
        variant: 'destructive',
      });
    }
  };

  // Filter accounts to show only expense accounts
  const expenseAccounts = React.useMemo(() => {
    if (!Array.isArray(accounts)) return [];
    return accounts.filter(acc =>
      acc.account_type.includes('Expense') && acc.is_active
    );
  }, [accounts]);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>
          {expense ? 'Edit Expense' : 'Create New Expense'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Description <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter expense description"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Amount <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expenseDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Date <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <DatePicker
                        date={field.value}
                        setDate={field.onChange}
                        placeholder="Select expense date"
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="accountId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Chart of Account <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {accountsLoading ? (
                          <SelectItem value="" disabled>Loading accounts...</SelectItem>
                        ) : accountsError ? (
                          <SelectItem value="" disabled>Error loading accounts</SelectItem>
                        ) : accounts?.data?.length === 0 ? (
                          <SelectItem value="" disabled>No accounts found</SelectItem>
                        ) : (
                          accounts?.data?.map((account) => (
                            <SelectItem key={account.id} value={account.id}>
                              {account.account_name || account.name} ({account.account_code || account.code})
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="vendorId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Vendor</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select vendor (optional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">No vendor</SelectItem>
                        {vendors?.data?.map((vendor) => (
                          <SelectItem key={vendor.id} value={vendor.id}>
                            {vendor.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category (optional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">No category</SelectItem>
                        {expenseCategories?.data?.map((category) => (
                          <SelectItem key={category.id} value={category.name}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Additional Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Method</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="credit_card">Credit Card</SelectItem>
                        <SelectItem value="debit_card">Debit Card</SelectItem>
                        <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                        <SelectItem value="check">Check</SelectItem>
                        <SelectItem value="online_payment">Online Payment</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="receiptUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Receipt URL</FormLabel>
                    <FormControl>
                      <Input
                        type="url"
                        placeholder="https://example.com/receipt.pdf"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes about this expense"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Action Buttons */}
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isCreating || isUpdating}
              >
                {isCreating || isUpdating ? 'Saving...' : expense ? 'Update Expense' : 'Create Expense'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
