import { useState, useCallback, useEffect } from 'react';

export function useModalState<T = any>() {
  const [modalData, setModalData] = useState<T | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const openModal = useCallback((data: T) => {
    setModalData(data);
    setIsOpen(true);
  }, []);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    
    // Add delay to ensure proper cleanup
    setTimeout(() => {
      setModalData(null);
      
      // Force cleanup of any modal artifacts
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
      document.body.style.pointerEvents = '';
      
      // Remove any potential scroll locks
      const body = document.body;
      body.classList.remove('overflow-hidden');
      
      // Force focus back to body
      if (document.activeElement && document.activeElement !== document.body) {
        (document.activeElement as HTMLElement).blur?.();
      }
      document.body.focus();
      
      // Remove any lingering modal overlays or portals
      const overlays = document.querySelectorAll('[data-radix-popper-content-wrapper], [data-radix-portal]');
      overlays.forEach(overlay => {
        try {
          if (overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
          }
        } catch (e) {
          // Ignore errors during cleanup
        }
      });
      
      // Reset any aria-hidden attributes
      const hiddenElements = document.querySelectorAll('[aria-hidden="true"]');
      hiddenElements.forEach(el => {
        if (el !== document.body && !el.closest('[role="dialog"]')) {
          el.removeAttribute('aria-hidden');
        }
      });
    }, 100);
  }, []);

  const handleOpenChange = useCallback((open: boolean) => {
    if (!open) {
      closeModal();
    }
  }, [closeModal]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
      document.body.style.pointerEvents = '';
    };
  }, []);

  return {
    modalData,
    isOpen,
    openModal,
    closeModal,
    handleOpenChange
  };
}
