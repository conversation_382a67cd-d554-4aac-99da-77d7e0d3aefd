# Budget Item Integration UI Guide

## Overview

The Budget Item Integration system allows you to link budget items to other modules in your accounting system, providing enhanced tracking, forecasting, and reporting capabilities. This guide explains how to use the UI components to manage these integrations.

## Accessing Budget Item Integrations

### 1. From the Budget Items Table

In the main budget page (`/budget`), each budget item row includes an **Integrations** column that shows:

- **Integration badges**: Visual indicators of linked modules
- **Plus button**: Click to add new integrations
- **Integration count**: Shows number of active integrations

#### Integration Badges

Each integration type has a unique color and icon:

- 🏪 **Inventory** (Blue): Link to inventory items for stock budgeting
- 👤 **Customer** (Green): Link to customers for revenue forecasting  
- 🚚 **Vendor** (Orange): Link to vendors for expense budgeting
- 🏢 **Project** (Purple): Link to projects for cost center budgeting
- 👥 **Employee** (Pink): Link to employees for payroll budgeting
- 📦 **Asset** (Indigo): Link to assets for depreciation budgeting
- 💳 **Bank Account** (Cyan): Link to bank accounts for cash flow budgeting
- 🧮 **Tax Category** (Yellow): Link to tax categories for tax planning

### 2. Integration Management Dialog

Click on any integration badge or the plus button to open the **Budget Item Integration Dialog**.

## Budget Item Integration Dialog

### Dialog Sections

#### 1. Active Integrations
- Shows all current integrations for the budget item
- Each integration displays:
  - Integration type with icon
  - Linked entity name
  - Category badge
  - **View** button: Opens the linked entity in a new tab
  - **Remove** button: Removes the integration

#### 2. Add New Integration

##### Integration Type Selection
Choose from 8 integration types organized by category:

**Operational:**
- Inventory Management
- Customer Management  
- Vendor Management
- Project/Cost Center

**HR:**
- Employee Management

**Asset:**
- Asset Management

**Banking:**
- Bank Account Integration

**Tax:**
- Tax Category Integration

##### Entity Selection
After selecting an integration type, the **Entity Selector** component provides:

- **Search functionality**: Find entities by name or code
- **Entity dropdown**: Select from available entities
- **Entity preview**: Shows selected entity details
- **View button**: Opens entity details in new tab
- **Integration benefits**: Lists specific benefits for the integration type

##### Notes (Optional)
Add custom notes about the integration relationship.

## Entity Selector Features

### Search and Filter
- Real-time search by entity name or code
- Displays entity count for each integration type
- Shows loading states during data fetching

### Entity Information
For each entity, the selector shows:
- **Name**: Primary entity identifier
- **Code**: Secondary identifier (if available)
- **Description**: Additional context
- **Status**: Current entity status

### Integration Benefits
Each integration type shows specific benefits:

#### Inventory Integration
- Track inventory-related budget allocations
- Monitor stock investment and reorder costs
- Forecast seasonal demand impact on budget

#### Customer Integration  
- Forecast revenue by customer segment
- Track customer acquisition costs
- Plan customer-specific marketing budgets

#### Vendor Integration
- Monitor vendor-specific spending
- Track contract renewal costs
- Optimize bulk purchase planning

#### Project Integration
- Allocate budget to specific projects
- Track project cost centers
- Monitor project profitability

#### Employee Integration
- Plan employee-specific costs
- Track training and development budgets
- Monitor compensation planning

#### Asset Integration
- Plan asset maintenance costs
- Track depreciation schedules
- Budget for asset replacements

#### Bank Account Integration
- Track account-specific cash flows
- Monitor banking fees and charges
- Plan account balance requirements

#### Tax Category Integration
- Plan tax provision requirements
- Track tax compliance costs
- Monitor tax optimization opportunities

## Navigation to Linked Modules

### Direct Navigation
Click any integration badge to navigate directly to the linked entity:

- **Inventory**: `/inventory/{itemId}`
- **Customer**: `/customers/{customerId}`
- **Vendor**: `/vendors/{vendorId}`
- **Project**: `/projects/{projectId}`
- **Employee**: `/employees/{employeeId}`
- **Asset**: `/assets/{assetId}`
- **Bank Account**: `/banking/accounts/{accountId}`
- **Tax Category**: `/tax/categories/{categoryId}`

### Module Overview
If no specific entity is linked, clicking navigates to the module's main page.

## Best Practices

### 1. Strategic Integration Planning
- Link budget items to the most relevant entities
- Consider the primary purpose of each budget item
- Use integrations to enhance reporting and forecasting

### 2. Integration Maintenance
- Regularly review and update integrations
- Remove outdated or irrelevant links
- Add notes to explain complex relationships

### 3. Reporting Benefits
- Use integrations for cross-module reporting
- Leverage linked data for variance analysis
- Create comprehensive financial dashboards

### 4. Workflow Optimization
- Integrate during budget creation for best results
- Use bulk operations for similar budget items
- Maintain consistent integration patterns

## Troubleshooting

### Common Issues

#### Integration Not Showing
- Verify the entity exists in the target module
- Check user permissions for the linked module
- Ensure the integration was saved successfully

#### Entity Not Found
- Confirm the entity hasn't been deleted
- Check if the entity is active/enabled
- Verify branch/organization context

#### Navigation Issues
- Ensure target module is accessible
- Check user permissions for the destination
- Verify the entity ID is valid

### Support
For additional support with budget integrations:
1. Check the system logs for error details
2. Verify API connectivity to linked modules
3. Contact system administrator for permission issues

## API Integration

The UI components interact with these API endpoints:

- `GET /api/budget/integrations/item-integrations/{budgetItemId}` - Get integrations
- `POST /api/budget/integrations/item-integrations` - Create integration
- `PUT /api/budget/integrations/item-integrations/{id}` - Update integration
- `DELETE /api/budget/integrations/item-integrations/{id}` - Delete integration

Each integration type also connects to its respective module API for entity data.

## Future Enhancements

Planned improvements include:
- Bulk integration management
- Integration templates
- Advanced filtering and search
- Integration analytics and reporting
- Automated integration suggestions
- Integration workflow automation
