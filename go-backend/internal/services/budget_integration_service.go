package services

import (
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"adc-account-backend/internal/models"
)

type BudgetIntegrationService struct {
	db *gorm.DB
}

func NewBudgetIntegrationService(db *gorm.DB) *BudgetIntegrationService {
	return &BudgetIntegrationService{db: db}
}

// GetIntegrationSettings retrieves all integration settings for a branch
func (s *BudgetIntegrationService) GetIntegrationSettings(branchID string) ([]models.BudgetIntegrationSettings, error) {
	var settings []models.BudgetIntegrationSettings

	err := s.db.Where("branch_id = ?", branchID).Find(&settings).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get integration settings: %w", err)
	}

	return settings, nil
}

// UpdateIntegrationSettings updates or creates integration settings
func (s *BudgetIntegrationService) UpdateIntegrationSettings(branchID string, settingsData []models.BudgetIntegrationSettings) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		for _, setting := range settingsData {
			setting.BranchID = branchID

			// Check if setting exists
			var existing models.BudgetIntegrationSettings
			err := tx.Where("branch_id = ? AND integration_type = ?", branchID, setting.IntegrationType).First(&existing).Error

			if err == gorm.ErrRecordNotFound {
				// Create new setting
				if err := tx.Create(&setting).Error; err != nil {
					return fmt.Errorf("failed to create integration setting: %w", err)
				}
			} else if err != nil {
				return fmt.Errorf("failed to check existing setting: %w", err)
			} else {
				// Update existing setting
				setting.ID = existing.ID
				setting.CreatedAt = existing.CreatedAt
				if err := tx.Save(&setting).Error; err != nil {
					return fmt.Errorf("failed to update integration setting: %w", err)
				}
			}
		}
		return nil
	})
}

// CreateBudgetItemIntegration creates optional relationships for budget items
func (s *BudgetIntegrationService) CreateBudgetItemIntegration(integration *models.BudgetItemIntegration) error {
	if err := s.validateBudgetItemIntegration(integration); err != nil {
		return err
	}

	return s.db.Create(integration).Error
}

// UpdateBudgetItemIntegration updates budget item integration relationships
func (s *BudgetIntegrationService) UpdateBudgetItemIntegration(id string, updates *models.BudgetItemIntegration) error {
	var existing models.BudgetItemIntegration
	if err := s.db.First(&existing, "id = ?", id).Error; err != nil {
		return fmt.Errorf("integration not found: %w", err)
	}

	if err := s.validateBudgetItemIntegration(updates); err != nil {
		return err
	}

	return s.db.Model(&existing).Updates(updates).Error
}

// GetBudgetItemIntegrations retrieves integrations for a budget item
func (s *BudgetIntegrationService) GetBudgetItemIntegrations(budgetItemID string) ([]models.BudgetItemIntegration, error) {
	var integrations []models.BudgetItemIntegration

	err := s.db.Where("budget_item_id = ?", budgetItemID).Find(&integrations).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get budget item integrations: %w", err)
	}

	return integrations, nil
}

// GenerateForecast creates budget forecasts based on integration type
func (s *BudgetIntegrationService) GenerateForecast(branchID, accountID, integrationType, forecastPeriod string, year int) (*models.BudgetForecast, error) {
	// Get historical data for forecasting
	historicalData, err := s.getHistoricalData(branchID, accountID, integrationType, year)
	if err != nil {
		return nil, fmt.Errorf("failed to get historical data: %w", err)
	}

	// Calculate predicted amount based on integration type
	predictedAmount, confidenceLevel := s.calculateForecast(historicalData, integrationType, forecastPeriod)

	forecast := &models.BudgetForecast{
		BranchID:              branchID,
		AccountID:             accountID,
		IntegrationType:       integrationType,
		ForecastPeriod:        forecastPeriod,
		Year:                  year,
		PredictedAmount:       predictedAmount,
		ConfidenceLevel:       confidenceLevel,
		BasedOnHistoricalData: len(historicalData) > 0,
	}

	// Set quarter if forecast period is quarterly
	if forecastPeriod == "quarterly" {
		quarter := int(((time.Now().Month() - 1) / 3) + 1)
		forecast.Quarter = &quarter
	}

	if err := s.db.Create(forecast).Error; err != nil {
		return nil, fmt.Errorf("failed to create forecast: %w", err)
	}

	return forecast, nil
}

// CreateAlert creates a budget alert
func (s *BudgetIntegrationService) CreateAlert(alert *models.BudgetAlert) error {
	return s.db.Create(alert).Error
}

// GetAlerts retrieves alerts for a branch
func (s *BudgetIntegrationService) GetAlerts(branchID string, unreadOnly bool) ([]models.BudgetAlert, error) {
	var alerts []models.BudgetAlert

	query := s.db.Where("branch_id = ?", branchID)
	if unreadOnly {
		query = query.Where("is_read = ?", false)
	}

	err := query.Order("created_at DESC").Find(&alerts).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get alerts: %w", err)
	}

	return alerts, nil
}

// MarkAlertAsRead marks an alert as read
func (s *BudgetIntegrationService) MarkAlertAsRead(alertID string) error {
	return s.db.Model(&models.BudgetAlert{}).Where("id = ?", alertID).Update("is_read", true).Error
}

// ResolveAlert marks an alert as resolved
func (s *BudgetIntegrationService) ResolveAlert(alertID string) error {
	now := time.Now()
	return s.db.Model(&models.BudgetAlert{}).Where("id = ?", alertID).Updates(map[string]interface{}{
		"is_resolved": true,
		"resolved_at": &now,
	}).Error
}

// LogIntegrationAction logs integration activities
func (s *BudgetIntegrationService) LogIntegrationAction(log *models.BudgetIntegrationLog) error {
	return s.db.Create(log).Error
}

// CheckThresholds checks budget thresholds and creates alerts if needed
func (s *BudgetIntegrationService) CheckThresholds(branchID string) error {
	// Get enabled integration settings
	var settings []models.BudgetIntegrationSettings
	if err := s.db.Where("branch_id = ? AND enabled = ?", branchID, true).Find(&settings).Error; err != nil {
		return fmt.Errorf("failed to get integration settings: %w", err)
	}

	for _, setting := range settings {
		if setting.Threshold != nil {
			if err := s.checkIntegrationThreshold(setting); err != nil {
				// Log error but continue with other checks
				fmt.Printf("Error checking threshold for %s: %v\n", setting.IntegrationType, err)
			}
		}
	}

	return nil
}

// Private helper methods

func (s *BudgetIntegrationService) validateBudgetItemIntegration(integration *models.BudgetItemIntegration) error {
	if integration.BudgetItemID == "" {
		return fmt.Errorf("budget item ID is required")
	}

	if integration.IntegrationType == "" {
		return fmt.Errorf("integration type is required")
	}

	// Validate that at least one optional relationship is set
	hasRelationship := integration.InventoryItemID != nil ||
		integration.CustomerID != nil ||
		integration.VendorID != nil ||
		integration.ProjectID != nil ||
		integration.EmployeeID != nil ||
		integration.AssetID != nil ||
		integration.BankAccountID != nil ||
		integration.TaxCategoryID != nil

	if !hasRelationship {
		return fmt.Errorf("at least one relationship must be specified")
	}

	return nil
}

func (s *BudgetIntegrationService) getHistoricalData(branchID, accountID, integrationType string, year int) ([]decimal.Decimal, error) {
	// This would query historical budget data based on integration type
	// For now, return empty slice - implement based on specific requirements
	return []decimal.Decimal{}, nil
}

func (s *BudgetIntegrationService) calculateForecast(historicalData []decimal.Decimal, integrationType, forecastPeriod string) (decimal.Decimal, decimal.Decimal) {
	// Simple forecasting logic - in production, use more sophisticated algorithms
	if len(historicalData) == 0 {
		return decimal.Zero, decimal.NewFromFloat(50.0) // 50% confidence for no data
	}

	// Calculate average
	sum := decimal.Zero
	for _, value := range historicalData {
		sum = sum.Add(value)
	}
	average := sum.Div(decimal.NewFromInt(int64(len(historicalData))))

	// Apply growth factor based on integration type
	growthFactor := decimal.NewFromFloat(1.05) // 5% default growth
	switch integrationType {
	case "inventory":
		growthFactor = decimal.NewFromFloat(1.03) // 3% growth for inventory
	case "customer":
		growthFactor = decimal.NewFromFloat(1.10) // 10% growth for customer revenue
	case "vendor":
		growthFactor = decimal.NewFromFloat(1.02) // 2% growth for vendor costs
	}

	predictedAmount := average.Mul(growthFactor)
	confidenceLevel := decimal.NewFromFloat(75.0) // 75% confidence with historical data

	return predictedAmount, confidenceLevel
}

func (s *BudgetIntegrationService) checkIntegrationThreshold(setting models.BudgetIntegrationSettings) error {
	// Implementation would check actual vs budget amounts and create alerts
	// This is a placeholder for the actual threshold checking logic
	return nil
}
