package services

import (
	"errors"
	"fmt"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type AccountService struct {
	db *gorm.DB
}

func NewAccountService(db *gorm.DB) *AccountService {
	return &AccountService{db: db}
}

// CreateAccountRequest represents a request to create an account
type CreateAccountRequest struct {
	BranchID    string             `json:"branchId" binding:"required"`
	Name        string             `json:"name" binding:"required"`
	Type        models.AccountType `json:"type" binding:"required"`
	Code        string             `json:"code" binding:"required"`
	Description *string            `json:"description"`
	ParentID    *string            `json:"parentId"`
	IsActive    bool               `json:"isActive"`
}

// CreateAccountForUserRequest represents a request to create an account for a user (without branch ID)
type CreateAccountForUserRequest struct {
	Name        string             `json:"name" binding:"required"`
	Type        models.AccountType `json:"type" binding:"required"`
	Code        string             `json:"code" binding:"required"`
	Description *string            `json:"description"`
	ParentID    *string            `json:"parentId"`
	IsActive    bool               `json:"isActive"`
}

// UpdateAccountRequest represents a request to update an account
type UpdateAccountRequest struct {
	Name        *string             `json:"name"`
	Type        *models.AccountType `json:"type"`
	Code        *string             `json:"code"`
	Description *string             `json:"description"`
	ParentID    *string             `json:"parentId"`
	IsActive    *bool               `json:"isActive"`
}

// AccountResponse represents an account response
type AccountResponse struct {
	ID          string                  `json:"id"`
	BranchID    string                  `json:"branchId"`
	Name        string                  `json:"name"`
	Type        models.AccountType      `json:"type"`
	Code        string                  `json:"code"`
	Description *string                 `json:"description"`
	ParentID    *string                 `json:"parentId"`
	IsActive    bool                    `json:"isActive"`
	Balance     decimal.Decimal         `json:"balance"`
	CreatedAt   string                  `json:"createdAt"`
	UpdatedAt   string                  `json:"updatedAt"`
	Parent      *models.ChartOfAccount  `json:"parent,omitempty"`
	Children    []models.ChartOfAccount `json:"children,omitempty"`
}

// GetAllAccounts returns all accounts with pagination
func (s *AccountService) GetAllAccounts(page, limit int, userID string) ([]AccountResponse, int64, error) {
	var accounts []models.ChartOfAccount
	var total int64

	// Build query to get accounts from branches user has access to
	query := s.db.Model(&models.ChartOfAccount{}).
		Joins("JOIN user_branch_permissions ON chart_of_accounts.branch_id = user_branch_permissions.branch_id").
		Where("user_branch_permissions.user_id = ? AND chart_of_accounts.is_active = ?", userID, true)

	// Count total accounts
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get accounts with pagination
	offset := (page - 1) * limit
	if err := query.Preload("Parent").Preload("Children").
		Offset(offset).Limit(limit).Find(&accounts).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]AccountResponse, 0, len(accounts))
	for _, account := range accounts {
		responses = append(responses, s.toAccountResponse(account))
	}

	return responses, total, nil
}

// GetAccountsByBranch returns accounts for a specific branch
func (s *AccountService) GetAccountsByBranch(branchID string, page, limit int, userID string) ([]AccountResponse, int64, error) {
	// Check if user has access to this branch
	if !s.hasBranchAccess(userID, branchID) {
		return nil, 0, errors.New("access denied to this branch")
	}

	var accounts []models.ChartOfAccount
	var total int64

	// Count total accounts for this branch
	if err := s.db.Model(&models.ChartOfAccount{}).Where("branch_id = ? AND is_active = ?", branchID, true).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get accounts with pagination
	offset := (page - 1) * limit
	if err := s.db.Where("branch_id = ? AND is_active = ?", branchID, true).
		Preload("Parent").Preload("Children").
		Order("code ASC").
		Offset(offset).Limit(limit).Find(&accounts).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]AccountResponse, 0, len(accounts))
	for _, account := range accounts {
		responses = append(responses, s.toAccountResponse(account))
	}

	return responses, total, nil
}

// GetAccountByID returns an account by ID
func (s *AccountService) GetAccountByID(id, userID string) (*AccountResponse, error) {
	var account models.ChartOfAccount

	// Get account and check access through branch
	if err := s.db.Joins("JOIN user_branch_permissions ON chart_of_accounts.branch_id = user_branch_permissions.branch_id").
		Where("chart_of_accounts.id = ? AND user_branch_permissions.user_id = ?", id, userID).
		Preload("Parent").Preload("Children").
		First(&account).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("account not found or access denied")
		}
		return nil, err
	}

	response := s.toAccountResponse(account)
	return &response, nil
}

// CreateAccount creates a new account
func (s *AccountService) CreateAccount(req CreateAccountRequest, userID string) (*AccountResponse, error) {
	// Check if user has access to this branch
	if !s.hasBranchAccess(userID, req.BranchID) {
		return nil, errors.New("access denied to this branch")
	}

	// Check if account code already exists for this branch
	var existingAccount models.ChartOfAccount
	if err := s.db.Where("branch_id = ? AND code = ?", req.BranchID, req.Code).First(&existingAccount).Error; err == nil {
		return nil, errors.New("account code already exists for this branch")
	}

	// Verify parent account exists if provided
	if req.ParentID != nil {
		var parentAccount models.ChartOfAccount
		if err := s.db.Where("id = ? AND branch_id = ?", *req.ParentID, req.BranchID).First(&parentAccount).Error; err != nil {
			return nil, errors.New("parent account not found or does not belong to this branch")
		}
	}

	// Create account
	account := models.ChartOfAccount{
		ID:          uuid.New().String(),
		BranchID:    req.BranchID,
		Name:        req.Name,
		Type:        req.Type,
		Code:        req.Code,
		Description: req.Description,
		ParentID:    req.ParentID,
		IsActive:    req.IsActive,
		Balance:     decimal.Zero,
	}

	if err := s.db.Create(&account).Error; err != nil {
		return nil, err
	}

	// Reload account with relationships
	if err := s.db.Preload("Parent").Preload("Children").
		Where("id = ?", account.ID).First(&account).Error; err != nil {
		return nil, err
	}

	response := s.toAccountResponse(account)
	return &response, nil
}

// UpdateAccount updates an existing account
func (s *AccountService) UpdateAccount(id string, req UpdateAccountRequest, userID string) (*AccountResponse, error) {
	var account models.ChartOfAccount

	// Get account and check access through branch
	if err := s.db.Joins("JOIN user_branch_permissions ON chart_of_accounts.branch_id = user_branch_permissions.branch_id").
		Where("chart_of_accounts.id = ? AND user_branch_permissions.user_id = ?", id, userID).
		First(&account).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("account not found or access denied")
		}
		return nil, err
	}

	// Check if account code is being changed and if it conflicts
	if req.Code != nil && *req.Code != account.Code {
		var existingAccount models.ChartOfAccount
		if err := s.db.Where("branch_id = ? AND code = ? AND id != ?", account.BranchID, *req.Code, id).First(&existingAccount).Error; err == nil {
			return nil, errors.New("account code already exists for this branch")
		}
	}

	// Verify parent account exists if being changed
	if req.ParentID != nil && *req.ParentID != "" {
		var parentAccount models.ChartOfAccount
		if err := s.db.Where("id = ? AND branch_id = ?", *req.ParentID, account.BranchID).First(&parentAccount).Error; err != nil {
			return nil, errors.New("parent account not found or does not belong to this branch")
		}

		// Prevent circular references
		if *req.ParentID == id {
			return nil, errors.New("account cannot be its own parent")
		}
	}

	// Update fields
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.Code != nil {
		updates["code"] = *req.Code
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.ParentID != nil {
		if *req.ParentID == "" {
			updates["parent_id"] = nil
		} else {
			updates["parent_id"] = *req.ParentID
		}
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	if len(updates) > 0 {
		if err := s.db.Model(&account).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	// Reload account with relationships
	if err := s.db.Preload("Parent").Preload("Children").
		Where("id = ?", id).First(&account).Error; err != nil {
		return nil, err
	}

	response := s.toAccountResponse(account)
	return &response, nil
}

// DeleteAccount soft deletes an account
func (s *AccountService) DeleteAccount(id, userID string) error {
	var account models.ChartOfAccount

	// Get account and check access through branch
	if err := s.db.Joins("JOIN user_branch_permissions ON chart_of_accounts.branch_id = user_branch_permissions.branch_id").
		Where("chart_of_accounts.id = ? AND user_branch_permissions.user_id = ?", id, userID).
		First(&account).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("account not found or access denied")
		}
		return err
	}

	// Check if account has children
	var childCount int64
	if err := s.db.Model(&models.ChartOfAccount{}).Where("parent_id = ?", id).Count(&childCount).Error; err != nil {
		return err
	}
	if childCount > 0 {
		return errors.New("cannot delete account with child accounts")
	}

	// Check if account has transactions (journal entries)
	var transactionCount int64
	if err := s.db.Model(&models.JournalEntryLine{}).Where("account_id = ?", id).Count(&transactionCount).Error; err != nil {
		return err
	}
	if transactionCount > 0 {
		return errors.New("cannot delete account with existing transactions")
	}

	// Soft delete by setting is_active to false
	return s.db.Model(&account).Update("is_active", false).Error
}

// BulkCreateAccountsRequest represents the request for bulk account creation
type BulkCreateAccountsRequest struct {
	BranchID string                 `json:"branchId" binding:"required"`
	Accounts []CreateAccountRequest `json:"accounts" binding:"required"`
}

// BulkCreateAccountsResponse represents the response for bulk account creation
type BulkCreateAccountsResponse struct {
	CreatedCount int               `json:"createdCount"`
	Accounts     []AccountResponse `json:"accounts"`
	Errors       []string          `json:"errors,omitempty"`
}

// BulkCreateAccounts creates multiple accounts in a single transaction
func (s *AccountService) BulkCreateAccounts(req BulkCreateAccountsRequest, userID string) (*BulkCreateAccountsResponse, error) {
	// Check if user has access to this branch
	if !s.hasBranchAccess(userID, req.BranchID) {
		return nil, errors.New("access denied to this branch")
	}

	var createdAccounts []AccountResponse
	var errorMessages []string

	// Start a database transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, accountReq := range req.Accounts {
		// Set the branch ID for each account
		accountReq.BranchID = req.BranchID

		// Check if account code already exists for this branch
		var existingAccount models.ChartOfAccount
		if err := tx.Where("branch_id = ? AND code = ?", req.BranchID, accountReq.Code).First(&existingAccount).Error; err == nil {
			errorMessages = append(errorMessages, fmt.Sprintf("Account code %s already exists", accountReq.Code))
			continue
		}

		// Verify parent account exists if provided
		if accountReq.ParentID != nil {
			var parentAccount models.ChartOfAccount
			if err := tx.Where("id = ? AND branch_id = ?", *accountReq.ParentID, req.BranchID).First(&parentAccount).Error; err != nil {
				errorMessages = append(errorMessages, fmt.Sprintf("Parent account not found for %s", accountReq.Code))
				continue
			}
		}

		// Create account
		account := models.ChartOfAccount{
			ID:          uuid.New().String(),
			BranchID:    req.BranchID,
			Name:        accountReq.Name,
			Type:        accountReq.Type,
			Code:        accountReq.Code,
			Description: accountReq.Description,
			ParentID:    accountReq.ParentID,
			IsActive:    accountReq.IsActive,
			Balance:     decimal.Zero,
		}

		if err := tx.Create(&account).Error; err != nil {
			errorMessages = append(errorMessages, fmt.Sprintf("Failed to create account %s: %v", accountReq.Code, err))
			continue
		}

		// Convert to response format
		createdAccounts = append(createdAccounts, s.toAccountResponse(account))
	}

	// Commit the transaction if we have any successful creations
	if len(createdAccounts) > 0 {
		if err := tx.Commit().Error; err != nil {
			return nil, fmt.Errorf("failed to commit transaction: %v", err)
		}
	} else {
		tx.Rollback()
	}

	return &BulkCreateAccountsResponse{
		CreatedCount: len(createdAccounts),
		Accounts:     createdAccounts,
		Errors:       errorMessages,
	}, nil
}

// Helper methods
func (s *AccountService) hasBranchAccess(userID, branchID string) bool {
	var count int64
	s.db.Model(&models.UserBranchPermission{}).
		Where("user_id = ? AND branch_id = ?", userID, branchID).
		Count(&count)
	return count > 0
}

// GetUserDefaultBranch returns the first branch ID that the user has access to
func (s *AccountService) GetUserDefaultBranch(userID string) (string, error) {
	var permission models.UserBranchPermission
	if err := s.db.Where("user_id = ?", userID).First(&permission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", errors.New("no branch found for user")
		}
		return "", err
	}
	return permission.BranchID, nil
}

// GetUnlinkedAccounts returns accounts that are not linked to bank accounts
func (s *AccountService) GetUnlinkedAccounts(accountType string, isActive *bool, userID string) ([]AccountResponse, int64, error) {
	var accounts []models.ChartOfAccount
	var total int64

	// Build query to get accounts from branches user has access to that are not linked to bank accounts
	query := s.db.Model(&models.ChartOfAccount{}).
		Joins("JOIN user_branch_permissions ON chart_of_accounts.branch_id = user_branch_permissions.branch_id").
		Where("user_branch_permissions.user_id = ?", userID).
		Where("chart_of_accounts.id NOT IN (SELECT DISTINCT chart_of_account_id FROM bank_accounts WHERE chart_of_account_id IS NOT NULL)")

	// Apply account type filter if provided
	if accountType != "" {
		query = query.Where("chart_of_accounts.type = ?", accountType)
	}

	// Apply active status filter if provided
	if isActive != nil {
		query = query.Where("chart_of_accounts.is_active = ?", *isActive)
	}

	// Count total accounts
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get accounts
	if err := query.Preload("Parent").Preload("Children").
		Order("chart_of_accounts.code ASC").Find(&accounts).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]AccountResponse, 0, len(accounts))
	for _, account := range accounts {
		responses = append(responses, s.toAccountResponse(account))
	}

	return responses, total, nil
}

// GetAssetAccounts returns all asset accounts for the authenticated user
func (s *AccountService) GetAssetAccounts(userID string) ([]AccountResponse, error) {
	var accounts []models.ChartOfAccount

	// Build query to get asset accounts from branches user has access to
	query := s.db.Model(&models.ChartOfAccount{}).
		Joins("JOIN user_organization_permissions ON chart_of_accounts.branch_id = user_organization_permissions.organization_id").
		Where("user_organization_permissions.user_id = ? AND chart_of_accounts.type = ? AND chart_of_accounts.is_active = ?",
			userID, models.AccountTypeAsset, true)

	// Get accounts
	if err := query.Preload("Parent").Preload("Children").
		Order("chart_of_accounts.code ASC").Find(&accounts).Error; err != nil {
		return nil, err
	}

	// Convert to response format
	responses := make([]AccountResponse, 0, len(accounts))
	for _, account := range accounts {
		responses = append(responses, s.toAccountResponse(account))
	}

	return responses, nil
}

// toAccountResponse converts a ChartOfAccount model to AccountResponse
func (s *AccountService) toAccountResponse(account models.ChartOfAccount) AccountResponse {
	return AccountResponse{
		ID:          account.ID,
		BranchID:    account.BranchID,
		Name:        account.Name,
		Type:        account.Type,
		Code:        account.Code,
		Description: account.Description,
		ParentID:    account.ParentID,
		IsActive:    account.IsActive,
		Balance:     account.Balance,
		CreatedAt:   account.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   account.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		Parent:      account.Parent,
		Children:    account.Children,
	}
}
