package routes

import (
	"adc-account-backend/internal/handlers"
	"adc-account-backend/internal/middleware"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all API routes
func SetupRoutes(router *gin.RouterGroup, container *services.Container) {
	// Initialize handlers
	authHandler := handlers.NewAuthHandler(container.AuthService)
	userHandler := handlers.NewUserHandler(container.UserService)
	organizationHandler := handlers.NewOrganizationHandler(container.OrganizationService)
	branchHandler := handlers.NewBranchHandler(container.BranchService, container.OrganizationService)
	settingsHandler := handlers.NewSettingsHandler(container.UserService, container.OrganizationService)

	invoiceHandler := handlers.NewInvoiceHandler(container.InvoiceService)
	billHandler := handlers.NewBillHandler(container.BillService)
	expenseHandler := handlers.NewExpenseHandler(container.ExpenseService)
	expenseCategoryHandler := handlers.NewExpenseCategoryHandler(container.ExpenseCategoryService)
	customerHandler := handlers.NewCustomerHandler(container.CustomerService)
	vendorHandler := handlers.NewVendorHandler(container.VendorService)
	accountHandler := handlers.NewAccountHandler(container.AccountService)
	journalEntryHandler := handlers.NewJournalEntryHandler(container.JournalEntryService)
	taxRateHandler := handlers.NewTaxRateHandler(container.TaxRateService)
	bankAccountHandler := handlers.NewBankAccountHandler(container.BankAccountService)
	bankTransactionHandler := handlers.NewBankTransactionHandler(container.BankTransactionService)
	bankReconciliationHandler := handlers.NewBankReconciliationHandler(container.BankReconciliationService)
	assetHandler := handlers.NewAssetHandler(container.AssetService)
	inventoryHandler := handlers.NewInventoryHandler(container.InventoryService)
	payrollHandler := handlers.NewPayrollHandler(container.PayrollService)
	budgetHandler := handlers.NewBudgetHandler(container.BudgetService)
	collectionCaseHandler := handlers.NewCollectionCaseHandler(container.CollectionCaseService)
	collectionActivityHandler := handlers.NewCollectionActivityHandler(container.CollectionActivityService)
	collectionTemplateHandler := handlers.NewCollectionTemplateHandler(container.CollectionTemplateService)
	recurringInvoiceHandler := handlers.NewRecurringInvoiceHandler(container.RecurringInvoiceService)
	budgetTemplateHandler := handlers.NewBudgetTemplateHandler(container.BudgetTemplateService)
	budgetIntegrationHandler := handlers.NewBudgetIntegrationHandler(container.BudgetIntegrationService)
	dashboardHandler := handlers.NewDashboardHandler(container.DashboardService)
	cashFlowItemHandler := handlers.NewCashFlowItemHandler(container.CashFlowItemService)
	cashFlowForecastHandler := handlers.NewCashFlowForecastHandler(container.CashFlowForecastService)
	recurringCashFlowHandler := handlers.NewRecurringCashFlowHandler(container.RecurringCashFlowItemService)
	cashFlowCategoryHandler := handlers.NewCashFlowCategoryHandler(container.CashFlowCategoryService)

	// Customer Portal handler
	customerPortalHandler := handlers.NewCustomerPortalHandler(
		container.InvoiceService,
		container.CustomerService,
		container.CustomerStatementService,
		container.PDFService,
		container.EmailService,
		container.PaymentService,
	)

	// PDF handler
	pdfHandler := handlers.NewPDFHandler(container.PDFService)

	// Email handler
	emailHandler := handlers.NewEmailHandler(container.EmailService)
	emailTemplateHandler := handlers.NewEmailTemplateHandler(container.EmailTemplateService)

	// Payment handler
	paymentHandler := handlers.NewPaymentHandler(container.PaymentService)

	// Authentication routes (no auth required)
	auth := router.Group("/auth")
	auth.Use(middleware.AuthRateLimit())
	{
		auth.POST("/login", authHandler.Login)
		auth.POST("/register", authHandler.Register)
		auth.POST("/google-oauth", authHandler.GoogleOAuth)
		auth.POST("/refresh", authHandler.RefreshToken)
		auth.POST("/logout", middleware.AuthMiddleware(container.Config), authHandler.Logout)
		auth.GET("/me", middleware.AuthMiddleware(container.Config), authHandler.GetCurrentUser)
	}

	// Protected routes
	protected := router.Group("")
	protected.Use(middleware.AuthMiddleware(container.Config))

	// User management routes
	users := protected.Group("/users")
	{
		users.GET("", userHandler.GetAllUsers)
		users.GET("/:id", userHandler.GetUserByID)
		users.POST("", userHandler.CreateUser)
		users.PUT("/:id", userHandler.UpdateUser)
		users.DELETE("/:id", userHandler.DeleteUser)
		users.PUT("/:id/preferences", userHandler.UpdateUserPreferences)
	}

	// Organization routes
	organizations := protected.Group("/organizations")
	{
		organizations.GET("", organizationHandler.GetAllOrganizations)
		organizations.GET("/:id", organizationHandler.GetOrganizationByID)
		organizations.POST("", organizationHandler.CreateOrganization)
		organizations.PUT("/:id", organizationHandler.UpdateOrganization)
		organizations.DELETE("/:id", organizationHandler.DeleteOrganization)

		// User management within organizations
		organizations.POST("/:id/users", organizationHandler.AddUserToOrganization)
		organizations.DELETE("/:id/users/:userId", organizationHandler.RemoveUserFromOrganization)

		// Branches under organization (by ID)
		organizations.GET("/:id/branches", branchHandler.GetBranchesByOrganization)
		organizations.POST("/:id/branches", branchHandler.CreateBranch)
		organizations.PUT("/:id/branches/:branchId", branchHandler.UpdateBranch)
		organizations.DELETE("/:id/branches/:branchId", branchHandler.DeleteBranch)
	}

	// Branch-specific routes
	branches := protected.Group("/branches")
	{
		branches.GET("", branchHandler.GetAllBranches)
		branches.GET("/:id", branchHandler.GetBranchByID)

		// Expense categories for specific branches
		branches.GET("/:id/expense-categories", expenseCategoryHandler.GetExpenseCategoriesByBranch)
		branches.POST("/:id/expense-categories", expenseCategoryHandler.CreateExpenseCategoryForBranch)
	}

	// Organization routes by slug
	organizationsBySlug := protected.Group("/organizations/slug")
	{
		organizationsBySlug.GET("/:slug", organizationHandler.GetOrganizationBySlug)

		// Branch routes within organizations (by slug)
		organizationsBySlug.GET("/:slug/branches", branchHandler.GetBranchesByOrganizationSlug)
		organizationsBySlug.POST("/:slug/branches", branchHandler.CreateBranchBySlug)
		organizationsBySlug.PUT("/:slug/branches/:branchId", branchHandler.UpdateBranchBySlug)
		organizationsBySlug.DELETE("/:slug/branches/:branchId", branchHandler.DeleteBranchBySlug)
	}

	// Global routes (not merchant-specific)
	customers := protected.Group("/customers")
	{
		customers.GET("", customerHandler.GetAllCustomers)
		customers.GET("/:id", customerHandler.GetCustomerByID)
		customers.POST("", customerHandler.CreateCustomerGlobal)
		customers.PUT("/:id", customerHandler.UpdateCustomerGlobal)
		customers.DELETE("/:id", customerHandler.DeleteCustomerGlobal)
	}

	vendors := protected.Group("/vendors")
	{
		vendors.GET("", vendorHandler.GetAllVendors)
		vendors.GET("/:id", vendorHandler.GetVendorByID)
		vendors.POST("", vendorHandler.CreateVendor)
		vendors.PUT("/:id", vendorHandler.UpdateVendor)
		vendors.DELETE("/:id", vendorHandler.DeleteVendor)
	}

	invoices := protected.Group("/invoices")
	{
		invoices.GET("", invoiceHandler.GetAllInvoices)
		invoices.GET("/:id", invoiceHandler.GetInvoiceByID)
	}

	bills := protected.Group("/bills")
	{
		bills.GET("", billHandler.GetAllBills)
		bills.GET("/:id", billHandler.GetBillByID)
		bills.POST("", billHandler.CreateBill)
		bills.PUT("/:id", billHandler.UpdateBill)
		bills.DELETE("/:id", billHandler.DeleteBill)
		bills.POST("/batch-update", billHandler.BatchUpdateBills)
		bills.POST("/batch-delete", billHandler.BatchDeleteBills)
		bills.GET("/payments", billHandler.GetBillPayments)
		bills.POST("/payments", billHandler.CreateBillPayment)
	}

	expenses := protected.Group("/expenses")
	{
		expenses.GET("", expenseHandler.GetAllExpenses)
		expenses.GET("/:id", expenseHandler.GetExpenseByID)
		expenses.POST("", expenseHandler.CreateExpenseGlobal)
		expenses.PUT("/:id", expenseHandler.UpdateExpenseGlobal)
		expenses.DELETE("/:id", expenseHandler.DeleteExpenseGlobal)
	}

	expenseCategories := protected.Group("/expense-categories")
	{
		expenseCategories.GET("", expenseCategoryHandler.GetExpenseCategories)
		expenseCategories.GET("/:id", expenseCategoryHandler.GetExpenseCategoryByID)
		expenseCategories.POST("", expenseCategoryHandler.CreateExpenseCategory)
		expenseCategories.PUT("/:id", expenseCategoryHandler.UpdateExpenseCategory)
		expenseCategories.DELETE("/:id", expenseCategoryHandler.DeleteExpenseCategory)
	}

	accounts := protected.Group("/accounts")
	{
		accounts.GET("", accountHandler.GetAllAccounts)
		accounts.GET("/unlinked", accountHandler.GetUnlinkedAccounts)
		accounts.GET("/:id", accountHandler.GetAccountByID)
		accounts.POST("", accountHandler.CreateAccountForUser)
		accounts.POST("/bulk", accountHandler.BulkCreateAccounts)
		accounts.PUT("/:id", accountHandler.UpdateAccount)
		accounts.DELETE("/:id", accountHandler.DeleteAccount)
		// Bank account linking endpoints
		accounts.GET("/:id/bank-account", bankAccountHandler.GetLinkedBankAccount)
	}

	// Chart of Accounts routes - for frontend compatibility
	coa := protected.Group("/coa")
	{
		coa.GET("/asset-accounts", accountHandler.GetAssetAccounts)
	}

	journalEntries := protected.Group("/journal-entries")
	{
		journalEntries.GET("", journalEntryHandler.GetAllJournalEntries)
		journalEntries.GET("/:id", journalEntryHandler.GetJournalEntryByID)
	}

	taxRates := protected.Group("/tax-rates")
	{
		taxRates.GET("", taxRateHandler.GetAllTaxRates)
		taxRates.GET("/:id", taxRateHandler.GetTaxRateByID)
		taxRates.GET("/calculate/:taxRateId", taxRateHandler.CalculateTax)
	}

	bankAccounts := protected.Group("/bank-accounts")
	{
		bankAccounts.GET("", bankAccountHandler.GetAllBankAccounts)
		bankAccounts.GET("/:id", bankAccountHandler.GetBankAccountByID)
	}

	// Banking group for frontend compatibility
	banking := protected.Group("/banking")
	{
		banking.GET("/check-coa-linked", bankAccountHandler.CheckCoaLinked)
		bankingAccounts := banking.Group("/accounts")
		{
			bankingAccounts.GET("", bankAccountHandler.GetAllBankAccounts)
			bankingAccounts.GET("/:id", bankAccountHandler.GetBankAccountByID)
			bankingAccounts.POST("", bankAccountHandler.CreateBankAccountForUser)
			bankingAccounts.PUT("/:id", bankAccountHandler.UpdateBankAccount)
			bankingAccounts.DELETE("/:id", bankAccountHandler.DeleteBankAccount)
		}
	}

	bankTransactions := protected.Group("/bank-transactions")
	{
		bankTransactions.GET("", bankTransactionHandler.GetAllBankTransactions)
		bankTransactions.GET("/:id", bankTransactionHandler.GetBankTransactionByID)
	}

	bankReconciliations := protected.Group("/bank-reconciliations")
	{
		bankReconciliations.GET("", bankReconciliationHandler.GetAllBankReconciliations)
		bankReconciliations.GET("/:id", bankReconciliationHandler.GetBankReconciliationByID)
	}

	assets := protected.Group("/assets")
	{
		assets.GET("", assetHandler.GetAllAssets)
		assets.GET("/:id", assetHandler.GetAssetByID)
	}

	// Inventory routes - matching frontend expectations
	inventory := protected.Group("/inventory")
	{
		// Inventory Items
		inventory.GET("/items", inventoryHandler.GetAllInventoryItems)
		inventory.GET("/items/:id", inventoryHandler.GetInventoryItemByID)
		inventory.POST("/items", inventoryHandler.CreateInventoryItem)
		inventory.PUT("/items/:id", inventoryHandler.UpdateInventoryItem)
		inventory.DELETE("/items/:id", inventoryHandler.DeleteInventoryItem)

		// Inventory Transactions
		inventory.GET("/transactions", inventoryHandler.GetAllInventoryTransactions)
		inventory.GET("/transactions/:id", inventoryHandler.GetInventoryTransactionByID)
		inventory.POST("/transactions", inventoryHandler.CreateInventoryTransaction)
	}

	employees := protected.Group("/employees")
	{
		employees.GET("", payrollHandler.GetAllEmployees)
		employees.GET("/:id", payrollHandler.GetEmployeeByID)
		employees.POST("", payrollHandler.CreateEmployee)
	}

	payrollRuns := protected.Group("/payroll-runs")
	{
		payrollRuns.GET("", payrollHandler.GetAllPayrollRuns)
		payrollRuns.GET("/:id", payrollHandler.GetPayrollRunByID)
		payrollRuns.POST("", payrollHandler.CreatePayrollRun)
	}

	payrollDetails := protected.Group("/payroll-details")
	{
		payrollDetails.GET("", payrollHandler.GetAllPayrollDetails)
		payrollDetails.GET("/:id", payrollHandler.GetPayrollDetailByID)
	}

	// Budget routes - matching frontend expectations
	budget := protected.Group("/budget")
	{
		// Budget Items
		budget.GET("/items", budgetHandler.GetBudgetItems)
		budget.POST("/items", budgetHandler.CreateBudgetItem)
		budget.GET("/items/:id", budgetHandler.GetBudgetItemByID)
		budget.PUT("/items/:id", budgetHandler.UpdateBudgetItem)
		budget.DELETE("/items/:id", budgetHandler.DeleteBudgetItem)

		// Bulk operations for budget items
		budget.DELETE("/items/bulk-delete", budgetHandler.BulkDeleteBudgetItems)
		budget.PUT("/items/bulk-update", budgetHandler.BulkUpdateBudgetItems)
		budget.POST("/items/export", budgetHandler.ExportBudgetItems)

		// Budget Reports
		budget.GET("/reports", budgetHandler.GetBudgetReport)
		budget.GET("/comparison", budgetHandler.GetBudgetComparison)

		// Budget Export/Import
		budget.GET("/export", budgetHandler.ExportBudget)
		budget.POST("/import", budgetHandler.ImportBudget)

		// Budget Templates
		budget.GET("/templates", budgetTemplateHandler.GetBudgetTemplates)
		budget.POST("/templates", budgetTemplateHandler.CreateBudgetTemplate)
		budget.GET("/templates/:id", budgetTemplateHandler.GetBudgetTemplateByID)
		budget.PUT("/templates/:id", budgetTemplateHandler.UpdateBudgetTemplate)
		budget.DELETE("/templates/:id", budgetTemplateHandler.DeleteBudgetTemplate)
		budget.POST("/templates/:id/apply", budgetTemplateHandler.ApplyBudgetTemplate)
		budget.GET("/templates/:id/export", budgetTemplateHandler.ExportBudgetTemplate)
		budget.POST("/templates/:id/duplicate", budgetTemplateHandler.DuplicateBudgetTemplate)

		// Budget Integration routes
		budget.GET("/integrations/settings", budgetIntegrationHandler.GetIntegrationSettings)
		budget.PUT("/integrations/settings", budgetIntegrationHandler.UpdateIntegrationSettings)
		budget.POST("/integrations/item-integrations", budgetIntegrationHandler.CreateBudgetItemIntegration)
		budget.PUT("/integrations/item-integrations/:id", budgetIntegrationHandler.UpdateBudgetItemIntegration)
		budget.GET("/integrations/item-integrations/:budgetItemId", budgetIntegrationHandler.GetBudgetItemIntegrations)
		budget.POST("/integrations/forecast", budgetIntegrationHandler.GenerateForecast)
		budget.GET("/integrations/alerts", budgetIntegrationHandler.GetAlerts)
		budget.PUT("/integrations/alerts/:id/read", budgetIntegrationHandler.MarkAlertAsRead)
		budget.PUT("/integrations/alerts/:id/resolve", budgetIntegrationHandler.ResolveAlert)
		budget.POST("/integrations/check-thresholds", budgetIntegrationHandler.CheckThresholds)

		// Integration-specific data endpoints
		budget.GET("/integrations/inventory", budgetIntegrationHandler.GetInventoryBudgetData)
		budget.GET("/integrations/customer", budgetIntegrationHandler.GetCustomerBudgetData)
		budget.GET("/integrations/vendor", budgetIntegrationHandler.GetVendorBudgetData)
		budget.GET("/integrations/tax", budgetIntegrationHandler.GetTaxBudgetData)
		budget.GET("/integrations/banking", budgetIntegrationHandler.GetBankingBudgetData)
	}

	// Legacy budget routes (for backward compatibility)
	budgetItems := protected.Group("/budget-items")
	{
		budgetItems.GET("", budgetHandler.GetAllBudgetItems)
		budgetItems.GET("/:id", budgetHandler.GetBudgetItemByID)
	}

	collectionCases := protected.Group("/collection-cases")
	{
		collectionCases.GET("", collectionCaseHandler.GetAllCollectionCases)
		collectionCases.GET("/:id", collectionCaseHandler.GetCollectionCaseByID)
	}

	collectionActivities := protected.Group("/collection-activities")
	{
		collectionActivities.GET("", collectionActivityHandler.GetAllCollectionActivities)
		collectionActivities.GET("/:id", collectionActivityHandler.GetCollectionActivityByID)
		collectionActivities.POST("", collectionActivityHandler.CreateCollectionActivity)
		collectionActivities.PUT("/:id", collectionActivityHandler.UpdateCollectionActivity)
		collectionActivities.DELETE("/:id", collectionActivityHandler.DeleteCollectionActivity)
		collectionActivities.GET("/summary", collectionActivityHandler.GetActivitySummary)
		collectionActivities.GET("/case/:caseId", collectionActivityHandler.GetCollectionActivitiesByCase)
		collectionActivities.GET("/creator/:creatorId", collectionActivityHandler.GetCollectionActivitiesByCreator)
	}

	recurringInvoices := protected.Group("/recurring-invoices")
	{
		recurringInvoices.GET("", recurringInvoiceHandler.GetAllRecurringInvoices)
		recurringInvoices.GET("/:id", recurringInvoiceHandler.GetRecurringInvoiceByID)
		recurringInvoices.PUT("/:id", recurringInvoiceHandler.UpdateRecurringInvoice)
		recurringInvoices.DELETE("/:id", recurringInvoiceHandler.DeleteRecurringInvoice)
		recurringInvoices.POST("/:id/generate", recurringInvoiceHandler.GenerateInvoice)
		recurringInvoices.PUT("/:id/status", recurringInvoiceHandler.UpdateRecurringInvoiceStatus)
		recurringInvoices.GET("/customer/:customerId", recurringInvoiceHandler.GetRecurringInvoicesByCustomer)
	}

	collectionTemplates := protected.Group("/collection-templates")
	{
		collectionTemplates.GET("", collectionTemplateHandler.GetAllCollectionTemplates)
		collectionTemplates.GET("/:id", collectionTemplateHandler.GetCollectionTemplateByID)
		collectionTemplates.PUT("/:id", collectionTemplateHandler.UpdateCollectionTemplate)
		collectionTemplates.DELETE("/:id", collectionTemplateHandler.DeleteCollectionTemplate)
		collectionTemplates.GET("/:id/steps", collectionTemplateHandler.GetCollectionTemplateSteps)
		collectionTemplates.POST("/:id/steps", collectionTemplateHandler.CreateCollectionTemplateStep)
		collectionTemplates.PUT("/:id/steps/:stepId", collectionTemplateHandler.UpdateCollectionTemplateStep)
		collectionTemplates.DELETE("/:id/steps/:stepId", collectionTemplateHandler.DeleteCollectionTemplateStep)
	}

	budgetTemplates := protected.Group("/budget-templates")
	{
		budgetTemplates.GET("", budgetTemplateHandler.GetAllBudgetTemplates)
		budgetTemplates.GET("/:id", budgetTemplateHandler.GetBudgetTemplateByID)
		budgetTemplates.PUT("/:id", budgetTemplateHandler.UpdateBudgetTemplate)
		budgetTemplates.DELETE("/:id", budgetTemplateHandler.DeleteBudgetTemplate)
		budgetTemplates.POST("/:id/apply", budgetTemplateHandler.ApplyBudgetTemplate)
		budgetTemplates.GET("/:id/items", budgetTemplateHandler.GetBudgetTemplateItems)
		budgetTemplates.POST("/:id/items", budgetTemplateHandler.CreateBudgetTemplateItem)
		budgetTemplates.PUT("/:id/items/:itemId", budgetTemplateHandler.UpdateBudgetTemplateItem)
		budgetTemplates.DELETE("/:id/items/:itemId", budgetTemplateHandler.DeleteBudgetTemplateItem)
	}

	// Cash Flow routes
	cashflow := protected.Group("/cashflow")
	{
		// Cash Flow Items
		cashflow.GET("/items", cashFlowItemHandler.GetCashFlowItems)
		cashflow.POST("/items", cashFlowItemHandler.CreateCashFlowItem)
		cashflow.GET("/items/:id", cashFlowItemHandler.GetCashFlowItem)
		cashflow.PUT("/items/:id", cashFlowItemHandler.UpdateCashFlowItem)
		cashflow.DELETE("/items/:id", cashFlowItemHandler.DeleteCashFlowItem)

		// Cash Flow Forecast
		cashflow.GET("/forecast", cashFlowForecastHandler.GetCashFlowForecast)
		cashflow.GET("/history", cashFlowForecastHandler.GetCashFlowHistory)
		cashflow.GET("/budget-comparison", cashFlowForecastHandler.GetBudgetComparison)

		// Recurring Cash Flow Items
		cashflow.GET("/recurring", recurringCashFlowHandler.GetRecurringCashFlowItems)
		cashflow.POST("/recurring", recurringCashFlowHandler.CreateRecurringCashFlowItem)
		cashflow.GET("/recurring/:id", recurringCashFlowHandler.GetRecurringCashFlowItem)
		cashflow.PUT("/recurring/:id", recurringCashFlowHandler.UpdateRecurringCashFlowItem)
		cashflow.DELETE("/recurring/:id", recurringCashFlowHandler.DeleteRecurringCashFlowItem)

		// Cash Flow Categories
		cashflow.GET("/categories", cashFlowCategoryHandler.GetCashFlowCategories)
		cashflow.POST("/categories", cashFlowCategoryHandler.CreateCashFlowCategory)
		cashflow.GET("/categories/:id", cashFlowCategoryHandler.GetCashFlowCategory)
		cashflow.PUT("/categories/:id", cashFlowCategoryHandler.UpdateCashFlowCategory)
		cashflow.DELETE("/categories/:id", cashFlowCategoryHandler.DeleteCashFlowCategory)
	}

	// Dashboard routes (protected)
	dashboard := protected.Group("/dashboard")
	{
		dashboard.GET("/financial-summary", dashboardHandler.GetFinancialSummary)
		dashboard.GET("/accounts-receivable-summary", dashboardHandler.GetAccountsReceivableSummary)
		dashboard.GET("/accounts-payable-summary", dashboardHandler.GetAccountsPayableSummary)
		dashboard.GET("/top-customers", dashboardHandler.GetTopCustomers)
		dashboard.GET("/top-vendors", dashboardHandler.GetTopVendors)
		dashboard.GET("/recent-transactions", dashboardHandler.GetRecentTransactions)
		dashboard.GET("/cash-flow", dashboardHandler.GetCashFlow)
	}

	// Test dashboard routes (unprotected for development testing)
	testDashboard := router.Group("/test-dashboard")
	{
		testDashboard.GET("/financial-summary", dashboardHandler.GetFinancialSummary)
		testDashboard.GET("/accounts-receivable-summary", dashboardHandler.GetAccountsReceivableSummary)
		testDashboard.GET("/accounts-payable-summary", dashboardHandler.GetAccountsPayableSummary)
		testDashboard.GET("/top-customers", dashboardHandler.GetTopCustomers)
		testDashboard.GET("/top-vendors", dashboardHandler.GetTopVendors)
		testDashboard.GET("/recent-transactions", dashboardHandler.GetRecentTransactions)
		testDashboard.GET("/cash-flow", dashboardHandler.GetCashFlow)
	}

	// Customer Portal routes (no auth required for customer portal login)
	customerPortal := router.Group("/customer-portal")
	{
		// Customer portal login
		customerPortal.POST("/login", customerPortalHandler.CustomerPortalLogin)

		// Customer portal protected routes (would need customer portal auth middleware)
		customerPortal.GET("/customers/:customerId/invoices", customerPortalHandler.GetCustomerInvoices)
		customerPortal.GET("/customers/:customerId/invoices/:invoiceId", customerPortalHandler.GetCustomerInvoiceByID)
		customerPortal.GET("/customers/:customerId/statements", customerPortalHandler.GetCustomerStatements)
		customerPortal.GET("/customers/:customerId/balance", customerPortalHandler.GetCustomerBalance)
		customerPortal.POST("/payments", customerPortalHandler.ProcessPayment)
	}

	// PDF generation routes
	pdf := router.Group("/pdf")
	{
		pdf.GET("/invoices/:invoiceId", pdfHandler.GenerateInvoicePDF)
		pdf.GET("/statements/:statementId", pdfHandler.GenerateStatementPDF)
	}

	// Email routes
	email := router.Group("/emails")
	{
		email.POST("/payment-confirmation", emailHandler.SendPaymentConfirmation)
		email.POST("/invoice-notification", emailHandler.SendInvoiceNotification)
		email.GET("/test-connection", emailHandler.TestEmailConnection)

		// Email templates routes
		templates := email.Group("/templates")
		{
			templates.GET("", emailTemplateHandler.GetAllEmailTemplates)
			templates.GET("/:id", emailTemplateHandler.GetEmailTemplateByID)
			templates.POST("", emailTemplateHandler.CreateEmailTemplate)
			templates.PUT("/:id", emailTemplateHandler.UpdateEmailTemplate)
			templates.DELETE("/:id", emailTemplateHandler.DeleteEmailTemplate)
		}
	}

	// Tax routes for frontend compatibility
	taxes := protected.Group("/taxes")
	{
		rates := taxes.Group("/rates")
		{
			rates.GET("", taxRateHandler.GetAllTaxRates)
			rates.GET("/:id", taxRateHandler.GetTaxRateByID)
			rates.GET("/calculate/:taxRateId", taxRateHandler.CalculateTax)
		}
	}

	// Settings routes
	settings := protected.Group("/settings")
	{
		// User preferences
		settings.GET("/preferences/:userId", settingsHandler.GetUserPreferences)
		settings.PUT("/preferences/:userId", settingsHandler.UpdateUserPreferences)

		// Company settings
		settings.GET("/company", settingsHandler.GetCompanySettings)
		settings.PUT("/company", settingsHandler.UpdateCompanySettings)

		// Accounting settings
		settings.GET("/accounting", settingsHandler.GetAccountingSettings)
		settings.PUT("/accounting", settingsHandler.UpdateAccountingSettings)
	}

	// Payment routes (Stripe integration)
	payments := router.Group("/payments")
	{
		payments.POST("/create-intent", paymentHandler.CreatePaymentIntent)
		payments.POST("/create-setup-intent", paymentHandler.CreateSetupIntent)
		payments.GET("/payment-methods", paymentHandler.GetPaymentMethods)
		payments.DELETE("/payment-methods", paymentHandler.DetachPaymentMethod)
		payments.POST("/webhook", paymentHandler.HandleWebhook)
		payments.POST("/process-success/:paymentIntentId", paymentHandler.ProcessPaymentSuccess)
	}
}
