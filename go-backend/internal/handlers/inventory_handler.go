package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

type InventoryHandler struct {
	inventoryService *services.InventoryService
}

func NewInventoryHandler(inventoryService *services.InventoryService) *InventoryHandler {
	return &InventoryHandler{
		inventoryService: inventoryService,
	}
}

// GetInventoryItems godoc
// @Summary Get all inventory items
// @Description Get all inventory items with pagination
// @Tags inventory
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} PaginatedResponse{data=[]services.InventoryItemResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /inventory-items [get]
func (h *InventoryHandler) GetInventoryItems(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	items, total, err := h.inventoryService.GetAllInventoryItems(page, limit, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       items,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// GetInventoryItemsByMerchant godoc
// @Summary Get inventory items by merchant
// @Description Get inventory items for a specific merchant with pagination
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} PaginatedResponse{data=[]services.InventoryItemResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/inventory-items [get]
func (h *InventoryHandler) GetInventoryItemsByMerchant(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	merchantID := c.Param("merchantId")
	if merchantID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Merchant ID is required"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	items, total, err := h.inventoryService.GetInventoryItemsByMerchant(merchantID, page, limit, userID)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       items,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// GetInventoryItem godoc
// @Summary Get inventory item by ID
// @Description Get a specific inventory item by ID
// @Tags inventory
// @Accept json
// @Produce json
// @Param id path string true "Inventory Item ID"
// @Success 200 {object} services.InventoryItemResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /inventory-items/{id} [get]
func (h *InventoryHandler) GetInventoryItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Inventory Item ID is required"})
		return
	}

	item, err := h.inventoryService.GetInventoryItemByID(id, userID)
	if err != nil {
		if err.Error() == "inventory item not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, item)
}

// CreateInventoryItem godoc
// @Summary Create a new inventory item
// @Description Create a new inventory item with stock tracking
// @Tags inventory
// @Accept json
// @Produce json
// @Param item body services.CreateInventoryItemRequest true "Inventory Item data"
// @Success 201 {object} services.InventoryItemResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /inventory-items [post]
func (h *InventoryHandler) CreateInventoryItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	var req services.CreateInventoryItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	item, err := h.inventoryService.CreateInventoryItem(req, userID)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, item)
}

// CreateInventoryTransaction godoc
// @Summary Create a new inventory transaction
// @Description Create a new inventory transaction (purchase, sale, adjustment, etc.)
// @Tags inventory
// @Accept json
// @Produce json
// @Param transaction body services.CreateInventoryTransactionRequest true "Inventory Transaction data"
// @Success 201 {object} services.InventoryTransactionResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /inventory-transactions [post]
func (h *InventoryHandler) CreateInventoryTransaction(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	var req services.CreateInventoryTransactionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	transaction, err := h.inventoryService.CreateInventoryTransaction(req, userID)
	if err != nil {
		if err.Error() == "inventory item not found or access denied" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, transaction)
}

// GetAllInventoryItems is an alias for GetInventoryItems for route compatibility
func (h *InventoryHandler) GetAllInventoryItems(c *gin.Context) {
	h.GetInventoryItems(c)
}

// GetInventoryItemByID is an alias for GetInventoryItem for route compatibility
func (h *InventoryHandler) GetInventoryItemByID(c *gin.Context) {
	h.GetInventoryItem(c)
}

// GetAllInventoryTransactions placeholder for route compatibility
func (h *InventoryHandler) GetAllInventoryTransactions(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "GetAllInventoryTransactions - Not implemented yet"})
}

// GetInventoryTransactionByID placeholder for route compatibility
func (h *InventoryHandler) GetInventoryTransactionByID(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "GetInventoryTransactionByID - Not implemented yet"})
}

// UpdateInventoryItem godoc
// @Summary Update an inventory item
// @Description Update an existing inventory item
// @Tags inventory
// @Accept json
// @Produce json
// @Param id path string true "Inventory Item ID"
// @Param item body services.UpdateInventoryItemRequest true "Updated inventory item data"
// @Success 200 {object} services.InventoryItemResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /inventory/items/{id} [put]
func (h *InventoryHandler) UpdateInventoryItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Inventory Item ID is required"})
		return
	}

	var req services.UpdateInventoryItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	item, err := h.inventoryService.UpdateInventoryItem(id, req, userID)
	if err != nil {
		if err.Error() == "inventory item not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		if err.Error() == "SKU already exists for this branch" {
			c.JSON(http.StatusConflict, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, item)
}

// DeleteInventoryItem godoc
// @Summary Delete an inventory item
// @Description Delete an inventory item by ID
// @Tags inventory
// @Accept json
// @Produce json
// @Param id path string true "Inventory Item ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /inventory/items/{id} [delete]
func (h *InventoryHandler) DeleteInventoryItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Inventory Item ID is required"})
		return
	}

	err := h.inventoryService.DeleteInventoryItem(id, userID)
	if err != nil {
		if err.Error() == "inventory item not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Inventory item deleted successfully"})
}

// GetInventoryTransactionsByMerchant placeholder for route compatibility
func (h *InventoryHandler) GetInventoryTransactionsByMerchant(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "GetInventoryTransactionsByMerchant - Not implemented yet"})
}

// UpdateInventoryTransaction placeholder for route compatibility
func (h *InventoryHandler) UpdateInventoryTransaction(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "UpdateInventoryTransaction - Not implemented yet"})
}

// DeleteInventoryTransaction placeholder for route compatibility
func (h *InventoryHandler) DeleteInventoryTransaction(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "DeleteInventoryTransaction - Not implemented yet"})
}
