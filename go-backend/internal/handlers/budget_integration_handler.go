package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

type BudgetIntegrationHandler struct {
	integrationService *services.BudgetIntegrationService
}

func NewBudgetIntegrationHandler(integrationService *services.BudgetIntegrationService) *BudgetIntegrationHandler {
	return &BudgetIntegrationHandler{
		integrationService: integrationService,
	}
}

// GetIntegrationSettings retrieves integration settings for a branch
func (h *BudgetIntegrationHandler) GetIntegrationSettings(c *gin.Context) {
	branchID := c.Query("branchId")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "branchId is required"})
		return
	}

	settings, err := h.integrationService.GetIntegrationSettings(branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{"settings": settings})
}

// UpdateIntegrationSettings updates integration settings for a branch
func (h *BudgetIntegrationHandler) UpdateIntegrationSettings(c *gin.Context) {
	branchID := c.Query("branchId")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "branchId is required"})
		return
	}

	var req struct {
		Settings []models.BudgetIntegrationSettings `json:"settings" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.integrationService.UpdateIntegrationSettings(branchID, req.Settings); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Integration settings updated successfully"})
}

// CreateBudgetItemIntegration creates optional relationships for budget items
func (h *BudgetIntegrationHandler) CreateBudgetItemIntegration(c *gin.Context) {
	var integration models.BudgetItemIntegration

	if err := c.ShouldBindJSON(&integration); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.integrationService.CreateBudgetItemIntegration(&integration); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":     "Budget item integration created successfully",
		"integration": integration,
	})
}

// UpdateBudgetItemIntegration updates budget item integration relationships
func (h *BudgetIntegrationHandler) UpdateBudgetItemIntegration(c *gin.Context) {
	integrationID := c.Param("id")

	var updates models.BudgetItemIntegration
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.integrationService.UpdateBudgetItemIntegration(integrationID, &updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Budget item integration updated successfully"})
}

// GetBudgetItemIntegrations retrieves integrations for a budget item
func (h *BudgetIntegrationHandler) GetBudgetItemIntegrations(c *gin.Context) {
	budgetItemID := c.Param("budgetItemId")

	integrations, err := h.integrationService.GetBudgetItemIntegrations(budgetItemID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"integrations": integrations})
}

// GenerateForecast generates budget forecasts
func (h *BudgetIntegrationHandler) GenerateForecast(c *gin.Context) {
	var req struct {
		BranchID        string `json:"branchId" binding:"required"`
		AccountID       string `json:"accountId" binding:"required"`
		IntegrationType string `json:"integrationType" binding:"required"`
		ForecastPeriod  string `json:"forecastPeriod" binding:"required"`
		Year            int    `json:"year" binding:"required,min=2000,max=2100"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	forecast, err := h.integrationService.GenerateForecast(
		req.BranchID,
		req.AccountID,
		req.IntegrationType,
		req.ForecastPeriod,
		req.Year,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":  "Forecast generated successfully",
		"forecast": forecast,
	})
}

// GetAlerts retrieves budget alerts
func (h *BudgetIntegrationHandler) GetAlerts(c *gin.Context) {
	branchID := c.Query("branchId")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "branchId is required"})
		return
	}

	unreadOnly := c.Query("unreadOnly") == "true"

	alerts, err := h.integrationService.GetAlerts(branchID, unreadOnly)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"alerts": alerts})
}

// MarkAlertAsRead marks an alert as read
func (h *BudgetIntegrationHandler) MarkAlertAsRead(c *gin.Context) {
	alertID := c.Param("id")

	if err := h.integrationService.MarkAlertAsRead(alertID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Alert marked as read"})
}

// ResolveAlert marks an alert as resolved
func (h *BudgetIntegrationHandler) ResolveAlert(c *gin.Context) {
	alertID := c.Param("id")

	if err := h.integrationService.ResolveAlert(alertID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Alert resolved successfully"})
}

// CheckThresholds manually triggers threshold checking
func (h *BudgetIntegrationHandler) CheckThresholds(c *gin.Context) {
	branchID := c.Query("branchId")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "branchId is required"})
		return
	}

	if err := h.integrationService.CheckThresholds(branchID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Threshold check completed"})
}

// GetInventoryBudgetData retrieves inventory-specific budget data
func (h *BudgetIntegrationHandler) GetInventoryBudgetData(c *gin.Context) {
	branchID := c.Query("branchId")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "branchId is required"})
		return
	}

	year, _ := strconv.Atoi(c.Query("year"))
	if year == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "year is required"})
		return
	}

	// This would integrate with inventory system to get stock levels, reorder points, etc.
	// For now, return placeholder data
	c.JSON(http.StatusOK, gin.H{
		"message": "Inventory budget data retrieved",
		"data": gin.H{
			"totalInventoryValue": 150000.00,
			"reorderAlerts":       3,
			"stockTurnover":       4.2,
			"forecastAccuracy":    85.5,
		},
	})
}

// GetCustomerBudgetData retrieves customer-specific budget data
func (h *BudgetIntegrationHandler) GetCustomerBudgetData(c *gin.Context) {
	branchID := c.Query("branchId")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "branchId is required"})
		return
	}

	year, _ := strconv.Atoi(c.Query("year"))
	if year == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "year is required"})
		return
	}

	// This would integrate with customer management system
	// For now, return placeholder data
	c.JSON(http.StatusOK, gin.H{
		"message": "Customer budget data retrieved",
		"data": gin.H{
			"totalRevenueForecast": 500000.00,
			"customerSegments":     5,
			"pipelineValue":        125000.00,
			"conversionRate":       23.5,
		},
	})
}

// GetVendorBudgetData retrieves vendor-specific budget data
func (h *BudgetIntegrationHandler) GetVendorBudgetData(c *gin.Context) {
	branchID := c.Query("branchId")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "branchId is required"})
		return
	}

	year, _ := strconv.Atoi(c.Query("year"))
	if year == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "year is required"})
		return
	}

	// This would integrate with vendor management system
	// For now, return placeholder data
	c.JSON(http.StatusOK, gin.H{
		"message": "Vendor budget data retrieved",
		"data": gin.H{
			"totalVendorSpend": 300000.00,
			"activeVendors":    15,
			"contractRenewals": 3,
			"costSavings":      12500.00,
		},
	})
}

// GetTaxBudgetData retrieves tax-specific budget data
func (h *BudgetIntegrationHandler) GetTaxBudgetData(c *gin.Context) {
	branchID := c.Query("branchId")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "branchId is required"})
		return
	}

	year, _ := strconv.Atoi(c.Query("year"))
	if year == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "year is required"})
		return
	}

	// This would integrate with tax management system
	// For now, return placeholder data
	c.JSON(http.StatusOK, gin.H{
		"message": "Tax budget data retrieved",
		"data": gin.H{
			"estimatedTaxLiability": 75000.00,
			"quarterlyPayments":     4,
			"complianceStatus":      "Current",
			"taxSavings":            8500.00,
		},
	})
}

// GetBankingBudgetData retrieves banking-specific budget data
func (h *BudgetIntegrationHandler) GetBankingBudgetData(c *gin.Context) {
	branchID := c.Query("branchId")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "branchId is required"})
		return
	}

	year, _ := strconv.Atoi(c.Query("year"))
	if year == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "year is required"})
		return
	}

	// This would integrate with banking system
	// For now, return placeholder data
	c.JSON(http.StatusOK, gin.H{
		"message": "Banking budget data retrieved",
		"data": gin.H{
			"cashFlowForecast":   250000.00,
			"bankFees":           1200.00,
			"reconciliationRate": 98.5,
			"liquidityRatio":     2.3,
		},
	})
}
