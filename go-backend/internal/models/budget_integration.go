package models

import (
	"time"
	"github.com/shopspring/decimal"
)

// BudgetIntegrationSettings represents the budget_integration_settings table
type BudgetIntegrationSettings struct {
	ID                   string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BranchID             string    `json:"branchId" gorm:"type:varchar(36);not null;index"`
	IntegrationType      string    `json:"integrationType" gorm:"type:varchar(50);not null;index"` // inventory, customer, vendor, tax, banking
	Enabled              bool      `json:"enabled" gorm:"default:false"`
	Threshold            *int      `json:"threshold" gorm:"type:int;check:threshold >= 0 AND threshold <= 100"`
	Frequency            *string   `json:"frequency" gorm:"type:varchar(20)"` // daily, weekly, monthly
	AutoApproval         *bool     `json:"autoApproval" gorm:"default:false"`
	Notifications        *bool     `json:"notifications" gorm:"default:true"`
	ReorderPoint         *int      `json:"reorderPoint" gorm:"type:int;check:reorder_point >= 0 AND reorder_point <= 100"`
	ForecastPeriod       *string   `json:"forecastPeriod" gorm:"type:varchar(20)"` // quarterly, annual, bi-annual
	ReconciliationMode   *string   `json:"reconciliationMode" gorm:"type:varchar(20)"` // automatic, manual, hybrid
	TaxCalculation       *string   `json:"taxCalculation" gorm:"type:varchar(20)"` // accrual, cash, hybrid
	CashFlowPrediction   *bool     `json:"cashFlowPrediction" gorm:"default:false"`
	CreatedAt            time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt            time.Time `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Branch Branch `json:"branch" gorm:"foreignKey:BranchID"`
}

// BudgetItemIntegration represents optional relationships for budget items
type BudgetItemIntegration struct {
	ID               string           `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BudgetItemID     string           `json:"budgetItemId" gorm:"type:varchar(36);not null;index"`
	
	// Optional relationships - all nullable
	InventoryItemID  *string          `json:"inventoryItemId" gorm:"type:varchar(36);index"`
	CustomerID       *string          `json:"customerId" gorm:"type:varchar(36);index"`
	VendorID         *string          `json:"vendorId" gorm:"type:varchar(36);index"`
	ProjectID        *string          `json:"projectId" gorm:"type:varchar(36);index"`
	EmployeeID       *string          `json:"employeeId" gorm:"type:varchar(36);index"`
	AssetID          *string          `json:"assetId" gorm:"type:varchar(36);index"`
	BankAccountID    *string          `json:"bankAccountId" gorm:"type:varchar(36);index"`
	TaxCategoryID    *string          `json:"taxCategoryId" gorm:"type:varchar(36);index"`
	
	// Integration-specific metadata
	IntegrationType  string           `json:"integrationType" gorm:"type:varchar(50);not null"` // inventory, customer, vendor, tax, banking
	Metadata         *string          `json:"metadata" gorm:"type:jsonb"` // Store additional integration-specific data
	CreatedAt        time.Time        `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt        time.Time        `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	BudgetItem       BudgetItem       `json:"budgetItem" gorm:"foreignKey:BudgetItemID"`
}

// BudgetForecast represents budget forecasting data
type BudgetForecast struct {
	ID                   string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BranchID             string          `json:"branchId" gorm:"type:varchar(36);not null;index"`
	AccountID            string          `json:"accountId" gorm:"type:varchar(36);not null;index"`
	IntegrationType      string          `json:"integrationType" gorm:"type:varchar(50);not null;index"`
	ForecastPeriod       string          `json:"forecastPeriod" gorm:"type:varchar(20);not null"` // quarterly, annual, bi-annual
	Year                 int             `json:"year" gorm:"not null;index"`
	Quarter              *int            `json:"quarter" gorm:"type:int;check:quarter >= 1 AND quarter <= 4"`
	PredictedAmount      decimal.Decimal `json:"predictedAmount" gorm:"type:decimal(15,2);not null;default:0"`
	ConfidenceLevel      decimal.Decimal `json:"confidenceLevel" gorm:"type:decimal(5,2);not null;default:0"` // 0-100%
	BasedOnHistoricalData bool           `json:"basedOnHistoricalData" gorm:"default:true"`
	CreatedAt            time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt            time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Branch               Branch          `json:"branch" gorm:"foreignKey:BranchID"`
	Account              ChartOfAccount  `json:"account" gorm:"foreignKey:AccountID"`
}

// BudgetAlert represents budget alerts and notifications
type BudgetAlert struct {
	ID                   string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BranchID             string          `json:"branchId" gorm:"type:varchar(36);not null;index"`
	BudgetItemID         *string         `json:"budgetItemId" gorm:"type:varchar(36);index"`
	IntegrationType      string          `json:"integrationType" gorm:"type:varchar(50);not null;index"`
	AlertType            string          `json:"alertType" gorm:"type:varchar(50);not null"` // threshold_exceeded, reorder_point, forecast_variance
	Severity             string          `json:"severity" gorm:"type:varchar(20);not null"` // low, medium, high, critical
	Title                string          `json:"title" gorm:"type:varchar(255);not null"`
	Message              string          `json:"message" gorm:"type:text;not null"`
	CurrentValue         decimal.Decimal `json:"currentValue" gorm:"type:decimal(15,2)"`
	ThresholdValue       decimal.Decimal `json:"thresholdValue" gorm:"type:decimal(15,2)"`
	IsRead               bool            `json:"isRead" gorm:"default:false"`
	IsResolved           bool            `json:"isResolved" gorm:"default:false"`
	ResolvedAt           *time.Time      `json:"resolvedAt"`
	CreatedAt            time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt            time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Branch               Branch          `json:"branch" gorm:"foreignKey:BranchID"`
	BudgetItem           *BudgetItem     `json:"budgetItem" gorm:"foreignKey:BudgetItemID"`
}

// BudgetIntegrationLog represents audit log for integration activities
type BudgetIntegrationLog struct {
	ID                   string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BranchID             string          `json:"branchId" gorm:"type:varchar(36);not null;index"`
	IntegrationType      string          `json:"integrationType" gorm:"type:varchar(50);not null;index"`
	Action               string          `json:"action" gorm:"type:varchar(100);not null"` // enabled, disabled, threshold_updated, forecast_generated
	EntityID             *string         `json:"entityId" gorm:"type:varchar(36)"` // ID of related entity (budget item, customer, etc.)
	EntityType           *string         `json:"entityType" gorm:"type:varchar(50)"` // budget_item, customer, vendor, etc.
	OldValue             *string         `json:"oldValue" gorm:"type:jsonb"`
	NewValue             *string         `json:"newValue" gorm:"type:jsonb"`
	UserID               *string         `json:"userId" gorm:"type:varchar(36)"` // User who performed the action
	AutomatedAction      bool            `json:"automatedAction" gorm:"default:false"`
	CreatedAt            time.Time       `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Branch               Branch          `json:"branch" gorm:"foreignKey:BranchID"`
}

// TableName methods to ensure correct table names
func (BudgetIntegrationSettings) TableName() string {
	return "budget_integration_settings"
}

func (BudgetItemIntegration) TableName() string {
	return "budget_item_integrations"
}

func (BudgetForecast) TableName() string {
	return "budget_forecasts"
}

func (BudgetAlert) TableName() string {
	return "budget_alerts"
}

func (BudgetIntegrationLog) TableName() string {
	return "budget_integration_logs"
}
