package migrations

import (
	"fmt"
	"log"

	"gorm.io/gorm"
)

// FixBudgetTemplatesSchema migrates budget_templates table from merchant_id to branch_id
func FixBudgetTemplatesSchema(db *gorm.DB) {
	fmt.Println("Starting budget_templates schema migration...")

	// Check if merchant_id column exists
	var merchantIdExists bool
	err := db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'budget_templates' AND column_name = 'merchant_id')").Scan(&merchantIdExists).Error
	if err != nil {
		log.Fatalf("Failed to check merchant_id column: %v", err)
	}

	// Check if branch_id column exists
	var branchIdExists bool
	err = db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'budget_templates' AND column_name = 'branch_id')").Scan(&branchIdExists).Error
	if err != nil {
		log.Fatalf("Failed to check branch_id column: %v", err)
	}

	if merchantIdExists && !branchIdExists {
		fmt.Println("Migrating budget_templates from merchant_id to branch_id...")

		// Add branch_id column
		if err := db.Exec("ALTER TABLE budget_templates ADD COLUMN branch_id VARCHAR(36)").Error; err != nil {
			log.Fatalf("Failed to add branch_id column: %v", err)
		}

		// Copy data from merchant_id to branch_id (assuming 1:1 mapping for now)
		if err := db.Exec("UPDATE budget_templates SET branch_id = merchant_id").Error; err != nil {
			log.Fatalf("Failed to copy merchant_id to branch_id: %v", err)
		}

		// Make branch_id NOT NULL
		if err := db.Exec("ALTER TABLE budget_templates ALTER COLUMN branch_id SET NOT NULL").Error; err != nil {
			log.Fatalf("Failed to make branch_id NOT NULL: %v", err)
		}

		// Add index for branch_id
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_budget_templates_branch_id ON budget_templates(branch_id)").Error; err != nil {
			log.Fatalf("Failed to create branch_id index: %v", err)
		}

		// Drop foreign key constraint for merchant_id
		if err := db.Exec("ALTER TABLE budget_templates DROP CONSTRAINT IF EXISTS fk_budget_templates_merchant").Error; err != nil {
			log.Printf("Warning: Failed to drop merchant foreign key constraint: %v", err)
		}

		// Drop merchant_id column
		if err := db.Exec("ALTER TABLE budget_templates DROP COLUMN merchant_id").Error; err != nil {
			log.Fatalf("Failed to drop merchant_id column: %v", err)
		}

		// Add foreign key constraint for branch_id
		if err := db.Exec("ALTER TABLE budget_templates ADD CONSTRAINT fk_budget_templates_branch FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE").Error; err != nil {
			log.Printf("Warning: Failed to add branch foreign key constraint: %v", err)
		}

		fmt.Println("Successfully migrated budget_templates table to use branch_id")
	} else if branchIdExists {
		fmt.Println("budget_templates table already uses branch_id")
	} else {
		fmt.Println("budget_templates table structure is unexpected")
	}

	fmt.Println("Budget templates migration completed successfully!")
}
