-- Migration: Create Budget Integration Tables
-- Description: Add support for advanced budget integrations with optional relationships

-- Budget Integration Settings Table
CREATE TABLE budget_integration_settings (
    id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    branch_id VARCHAR(36) NOT NULL,
    integration_type VARCHAR(50) NOT NULL,
    enabled BOOLEAN DEFAULT false,
    threshold INTEGER CHECK (threshold >= 0 AND threshold <= 100),
    frequency VARCHAR(20), -- daily, weekly, monthly
    auto_approval BOOLEAN DEFAULT false,
    notifications BOOLEAN DEFAULT true,
    reorder_point INTEGER CHECK (reorder_point >= 0 AND reorder_point <= 100),
    forecast_period VARCHAR(20), -- quarterly, annual, bi-annual
    reconciliation_mode VARCHAR(20), -- automatic, manual, hybrid
    tax_calculation VARCHAR(20), -- accrual, cash, hybrid
    cash_flow_prediction BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT fk_budget_integration_settings_branch 
        FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    CONSTRAINT uk_budget_integration_settings_branch_type 
        UNIQUE (branch_id, integration_type)
);

-- Budget Item Integrations Table (Optional Relationships)
CREATE TABLE budget_item_integrations (
    id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    budget_item_id VARCHAR(36) NOT NULL,
    
    -- Optional relationships - all nullable
    inventory_item_id VARCHAR(36),
    customer_id VARCHAR(36),
    vendor_id VARCHAR(36),
    project_id VARCHAR(36),
    employee_id VARCHAR(36),
    asset_id VARCHAR(36),
    bank_account_id VARCHAR(36),
    tax_category_id VARCHAR(36),
    
    -- Integration metadata
    integration_type VARCHAR(50) NOT NULL,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT fk_budget_item_integrations_budget_item 
        FOREIGN KEY (budget_item_id) REFERENCES budget_items(id) ON DELETE CASCADE
    
    -- Note: Foreign key constraints for optional relationships would be added 
    -- when those modules are implemented, e.g.:
    -- CONSTRAINT fk_budget_item_integrations_inventory 
    --     FOREIGN KEY (inventory_item_id) REFERENCES inventory_items(id) ON DELETE SET NULL,
    -- CONSTRAINT fk_budget_item_integrations_customer 
    --     FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    -- etc.
);

-- Budget Forecasts Table
CREATE TABLE budget_forecasts (
    id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    branch_id VARCHAR(36) NOT NULL,
    account_id VARCHAR(36) NOT NULL,
    integration_type VARCHAR(50) NOT NULL,
    forecast_period VARCHAR(20) NOT NULL, -- quarterly, annual, bi-annual
    year INTEGER NOT NULL,
    quarter INTEGER CHECK (quarter >= 1 AND quarter <= 4),
    predicted_amount NUMERIC(15,2) NOT NULL DEFAULT 0,
    confidence_level NUMERIC(5,2) NOT NULL DEFAULT 0, -- 0-100%
    based_on_historical_data BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT fk_budget_forecasts_branch 
        FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    CONSTRAINT fk_budget_forecasts_account 
        FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id) ON DELETE CASCADE,
    CONSTRAINT budget_forecasts_year_valid 
        CHECK (year >= 2000 AND year <= 2100)
);

-- Budget Alerts Table
CREATE TABLE budget_alerts (
    id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    branch_id VARCHAR(36) NOT NULL,
    budget_item_id VARCHAR(36),
    integration_type VARCHAR(50) NOT NULL,
    alert_type VARCHAR(50) NOT NULL, -- threshold_exceeded, reorder_point, forecast_variance
    severity VARCHAR(20) NOT NULL, -- low, medium, high, critical
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    current_value NUMERIC(15,2),
    threshold_value NUMERIC(15,2),
    is_read BOOLEAN DEFAULT false,
    is_resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT fk_budget_alerts_branch 
        FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    CONSTRAINT fk_budget_alerts_budget_item 
        FOREIGN KEY (budget_item_id) REFERENCES budget_items(id) ON DELETE SET NULL
);

-- Budget Integration Logs Table (Audit Trail)
CREATE TABLE budget_integration_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    branch_id VARCHAR(36) NOT NULL,
    integration_type VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL, -- enabled, disabled, threshold_updated, forecast_generated
    entity_id VARCHAR(36), -- ID of related entity
    entity_type VARCHAR(50), -- budget_item, customer, vendor, etc.
    old_value JSONB,
    new_value JSONB,
    user_id VARCHAR(36), -- User who performed the action
    automated_action BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT fk_budget_integration_logs_branch 
        FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE
);

-- Indexes for Performance
CREATE INDEX idx_budget_integration_settings_branch_id ON budget_integration_settings(branch_id);
CREATE INDEX idx_budget_integration_settings_type ON budget_integration_settings(integration_type);
CREATE INDEX idx_budget_integration_settings_enabled ON budget_integration_settings(enabled);

CREATE INDEX idx_budget_item_integrations_budget_item_id ON budget_item_integrations(budget_item_id);
CREATE INDEX idx_budget_item_integrations_type ON budget_item_integrations(integration_type);
CREATE INDEX idx_budget_item_integrations_inventory_item_id ON budget_item_integrations(inventory_item_id);
CREATE INDEX idx_budget_item_integrations_customer_id ON budget_item_integrations(customer_id);
CREATE INDEX idx_budget_item_integrations_vendor_id ON budget_item_integrations(vendor_id);

CREATE INDEX idx_budget_forecasts_branch_id ON budget_forecasts(branch_id);
CREATE INDEX idx_budget_forecasts_account_id ON budget_forecasts(account_id);
CREATE INDEX idx_budget_forecasts_type ON budget_forecasts(integration_type);
CREATE INDEX idx_budget_forecasts_period ON budget_forecasts(year, quarter);

CREATE INDEX idx_budget_alerts_branch_id ON budget_alerts(branch_id);
CREATE INDEX idx_budget_alerts_budget_item_id ON budget_alerts(budget_item_id);
CREATE INDEX idx_budget_alerts_type ON budget_alerts(integration_type);
CREATE INDEX idx_budget_alerts_unread ON budget_alerts(is_read) WHERE is_read = false;
CREATE INDEX idx_budget_alerts_unresolved ON budget_alerts(is_resolved) WHERE is_resolved = false;

CREATE INDEX idx_budget_integration_logs_branch_id ON budget_integration_logs(branch_id);
CREATE INDEX idx_budget_integration_logs_type ON budget_integration_logs(integration_type);
CREATE INDEX idx_budget_integration_logs_action ON budget_integration_logs(action);
CREATE INDEX idx_budget_integration_logs_created_at ON budget_integration_logs(created_at);

-- Insert default integration settings for existing branches
INSERT INTO budget_integration_settings (branch_id, integration_type, enabled, threshold, frequency, notifications)
SELECT 
    id as branch_id,
    'inventory' as integration_type,
    false as enabled,
    75 as threshold,
    'weekly' as frequency,
    true as notifications
FROM branches
WHERE NOT EXISTS (
    SELECT 1 FROM budget_integration_settings 
    WHERE branch_id = branches.id AND integration_type = 'inventory'
);

INSERT INTO budget_integration_settings (branch_id, integration_type, enabled, threshold, frequency, notifications)
SELECT 
    id as branch_id,
    'customer' as integration_type,
    false as enabled,
    85 as threshold,
    'monthly' as frequency,
    true as notifications
FROM branches
WHERE NOT EXISTS (
    SELECT 1 FROM budget_integration_settings 
    WHERE branch_id = branches.id AND integration_type = 'customer'
);

INSERT INTO budget_integration_settings (branch_id, integration_type, enabled, threshold, frequency, notifications)
SELECT 
    id as branch_id,
    'vendor' as integration_type,
    false as enabled,
    90 as threshold,
    'monthly' as frequency,
    true as notifications
FROM branches
WHERE NOT EXISTS (
    SELECT 1 FROM budget_integration_settings 
    WHERE branch_id = branches.id AND integration_type = 'vendor'
);

INSERT INTO budget_integration_settings (branch_id, integration_type, enabled, threshold, frequency, notifications)
SELECT 
    id as branch_id,
    'tax' as integration_type,
    false as enabled,
    95 as threshold,
    'monthly' as frequency,
    true as notifications
FROM branches
WHERE NOT EXISTS (
    SELECT 1 FROM budget_integration_settings 
    WHERE branch_id = branches.id AND integration_type = 'tax'
);

INSERT INTO budget_integration_settings (branch_id, integration_type, enabled, threshold, frequency, notifications)
SELECT 
    id as branch_id,
    'banking' as integration_type,
    false as enabled,
    100 as threshold,
    'daily' as frequency,
    true as notifications
FROM branches
WHERE NOT EXISTS (
    SELECT 1 FROM budget_integration_settings 
    WHERE branch_id = branches.id AND integration_type = 'banking'
);

-- Add comments for documentation
COMMENT ON TABLE budget_integration_settings IS 'Configuration settings for budget integrations with other business modules';
COMMENT ON TABLE budget_item_integrations IS 'Optional relationships between budget items and other business entities';
COMMENT ON TABLE budget_forecasts IS 'AI-generated budget forecasts based on historical data and integration types';
COMMENT ON TABLE budget_alerts IS 'Automated alerts for budget thresholds, reorder points, and forecast variances';
COMMENT ON TABLE budget_integration_logs IS 'Audit trail for all budget integration activities and changes';
