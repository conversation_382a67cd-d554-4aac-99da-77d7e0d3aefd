package main

import (
	"log"

	"adc-account-backend/internal/config"
	"adc-account-backend/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Connect to database
	db, err := gorm.Open(postgres.Open(cfg.DatabaseURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	log.Println("Creating default vendor...")

	// Get the first branch ID (assuming there's at least one branch)
	var branchID string
	err = db.Model(&models.Branch{}).Select("id").First(&branchID).Error
	if err != nil {
		log.Fatalf("Failed to get branch ID: %v", err)
	}

	log.Printf("Using branch ID: %s", branchID)

	// Check if default vendor already exists
	var existingVendor models.Vendor
	err = db.Where("name = ? AND branch_id = ?", "Default Vendor", branchID).First(&existingVendor).Error
	if err == nil {
		log.Printf("Default vendor already exists: %s", existingVendor.ID)
		return
	}

	// Create default vendor
	vendor := models.Vendor{
		BranchID: branchID,
		Name:     "Default Vendor",
		Email:    stringPtr("<EMAIL>"),
		Phone:    stringPtr("************"),
		Address:  stringPtr("Default Address"),
		IsActive: true,
	}

	if err := db.Create(&vendor).Error; err != nil {
		log.Fatalf("Failed to create default vendor: %v", err)
	}

	log.Printf("Created default vendor: %s - %s", vendor.ID, vendor.Name)
	log.Println("Default vendor creation completed!")
}

func stringPtr(s string) *string {
	return &s
}
